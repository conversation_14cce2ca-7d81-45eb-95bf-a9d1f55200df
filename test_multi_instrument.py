#!/usr/bin/env python3
"""
Test script to validate multi-instrument MIDI rendering functionality.
This script tests the enhanced SF2 parser and MIDI processing system.
"""

import os
import sys
from hexsf2newparser import SF2New<PERSON>ars<PERSON>

def test_sf2_multi_instrument_support(sf2_path):
    """Test the enhanced SF2 parser methods for multi-instrument support."""
    print(f"Testing SF2 parser with: {sf2_path}")
    
    if not os.path.exists(sf2_path):
        print(f"Error: SF2 file not found: {sf2_path}")
        return False
    
    try:
        # Initialize and parse the SF2 file
        parser = SF2NewParser(sf2_path)
        parser.parse()
        
        print(f"✓ Successfully parsed SF2 file")
        print(f"  - Found {len(parser.presets)} presets")
        print(f"  - Found {len(parser.instruments)} instruments")
        print(f"  - Found {len(parser.sample_headers)} samples")
        
        # Test preset listing
        preset_list = parser.get_preset_list()
        print(f"✓ get_preset_list() returned {len(preset_list)} presets")
        
        # Show first few presets
        for i, preset_info in enumerate(preset_list[:5]):
            print(f"  - Preset {i}: {preset_info['name']} (Bank {preset_info['bank']}, Program {preset_info['program']})")
        
        # Test General MIDI mapping
        gm_mapping = parser.get_general_midi_mapping()
        print(f"✓ get_general_midi_mapping() mapped {len(gm_mapping)} GM programs")
        
        # Show some GM mappings
        for program in [0, 1, 9, 24, 40, 56, 73]:  # Piano, Bright Piano, Glockenspiel, Guitar, Violin, Trumpet, Flute
            if program in gm_mapping:
                preset_idx = gm_mapping[program]
                preset_name = parser.presets[preset_idx].name if preset_idx < len(parser.presets) else "Unknown"
                print(f"  - GM Program {program} -> Preset {preset_idx} ({preset_name})")
        
        # Test channel mapping
        channel_mapping = parser.create_channel_preset_mapping()
        print(f"✓ create_channel_preset_mapping() mapped {len(channel_mapping)} channels")
        
        # Test drum detection
        drum_preset = parser.find_drum_preset()
        if drum_preset is not None:
            preset = parser.presets[drum_preset]
            print(f"✓ Found drum kit: Preset {drum_preset} - '{preset.name}' (Bank {preset.bank_num})")
        else:
            print("⚠ No drum kit found in soundfont")

        # Test GM drum mapping
        drum_mapping = parser.get_gm_drum_mapping()
        print(f"✓ get_gm_drum_mapping() returned {len(drum_mapping)} drum sounds")

        # Test drum channel mapping specifically
        test_programs = {i: 0 for i in range(16)}  # All piano
        test_programs[9] = -1  # Mark channel 10 as drums
        drum_channel_mapping = parser.create_channel_preset_mapping(test_programs)
        drum_channel_preset = drum_channel_mapping[9]
        if drum_preset is not None and drum_channel_preset == drum_preset:
            print(f"✓ Channel 10 correctly mapped to drum preset {drum_preset}")
        else:
            print(f"⚠ Channel 10 mapped to preset {drum_channel_preset}, expected {drum_preset}")

        # Test sample loading for a specific preset
        if len(parser.presets) > 0:
            preset_idx = 0
            sample_map = parser.get_samples_for_preset(preset_idx)
            print(f"✓ get_samples_for_preset({preset_idx}) returned {len(sample_map)} note mappings")

            # Show some note mappings
            notes_to_show = [60, 64, 67, 72]  # C4, E4, G4, C5
            for note in notes_to_show:
                if note in sample_map:
                    sample_id, generators = sample_map[note]
                    sample_name = parser.sample_headers[sample_id].name if sample_id < len(parser.sample_headers) else "Unknown"
                    print(f"  - Note {note} -> Sample {sample_id} ({sample_name})")

        # Test drum sample loading if drum preset exists
        if drum_preset is not None:
            drum_sample_map = parser.get_samples_for_preset(drum_preset)
            print(f"✓ Drum preset has {len(drum_sample_map)} sample mappings")

            # Show some common drum notes
            drum_notes = [36, 38, 42, 46, 49, 51]  # Kick, snare, hi-hat, etc.
            for note in drum_notes:
                if note in drum_sample_map:
                    sample_id, generators = drum_sample_map[note]
                    sample_name = parser.sample_headers[sample_id].name if sample_id < len(parser.sample_headers) else "Unknown"
                    drum_name = drum_mapping.get(note, f"Note {note}")
                    print(f"  - {drum_name} (Note {note}) -> Sample {sample_id} ({sample_name})")

        return True
        
    except Exception as e:
        print(f"✗ Error testing SF2 parser: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_midi_program_analysis(midi_path):
    """Test MIDI program change analysis."""
    print(f"\nTesting MIDI program analysis with: {midi_path}")
    
    if not os.path.exists(midi_path):
        print(f"Error: MIDI file not found: {midi_path}")
        return False
    
    try:
        # Import the analyze_midi_programs function
        sys.path.append('.')
        from importlib import import_module
        midi_module = import_module('midi-to-audio-with-correct-bpm - Copy (3) - Copy')
        analyze_midi_programs = midi_module.analyze_midi_programs
        
        # Analyze the MIDI file
        channel_programs = analyze_midi_programs(midi_path)
        print(f"✓ Successfully analyzed MIDI file")
        print(f"  - Found program assignments for {len(channel_programs)} channels")
        
        # Show channel programs
        for channel, program in channel_programs.items():
            if channel == 9:
                print(f"  - Channel {channel + 1}: Drums")
            else:
                print(f"  - Channel {channel + 1}: GM Program {program}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error analyzing MIDI programs: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("Multi-Instrument MIDI Rendering Test")
    print("=" * 40)
    
    # Look for test files in the current directory
    sf2_files = [f for f in os.listdir('.') if f.lower().endswith('.sf2')]
    midi_files = [f for f in os.listdir('.') if f.lower().endswith(('.mid', '.midi'))]
    
    if not sf2_files:
        print("No SF2 files found in current directory. Please add a SoundFont file to test.")
        return
    
    if not midi_files:
        print("No MIDI files found in current directory. Please add a MIDI file to test.")
        return
    
    # Test SF2 parser with the first available SF2 file
    sf2_path = sf2_files[0]
    sf2_success = test_sf2_multi_instrument_support(sf2_path)
    
    # Test MIDI analysis with the first available MIDI file
    midi_path = midi_files[0]
    midi_success = test_midi_program_analysis(midi_path)
    
    print("\n" + "=" * 40)
    if sf2_success and midi_success:
        print("✓ All tests passed! Multi-instrument support is working.")
    else:
        print("✗ Some tests failed. Please check the error messages above.")
    
    print(f"\nTo test the full rendering pipeline, run the main GUI application")
    print(f"with the SF2 file '{sf2_path}' and MIDI file '{midi_path}'.")

if __name__ == "__main__":
    main()
