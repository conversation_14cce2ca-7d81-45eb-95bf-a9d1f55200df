#!/usr/bin/env python3
"""
Debug script to identify which samples are causing tapping sounds
and check if the loop system is working properly.
"""

import json
import numpy as np
from hexsf2newparser import SF2<PERSON>ewPars<PERSON>

def debug_tapping_samples():
    """Find and analyze samples that might be causing tapping sounds."""
    
    # Load settings
    with open('hexsyn_settings.json', 'r') as f:
        settings = json.load(f)
    sf2_path = settings.get('sf2_path', '')
    
    print(f"Analyzing samples in: {sf2_path}")
    
    # Initialize parser
    parser = SF2NewParser(sf2_path)
    parser.parse()
    
    print(f"Total samples: {len(parser.sample_headers)}")
    
    # Find problematic samples
    very_short_samples = []
    short_with_loops = []
    short_without_loops = []
    
    for i, header in enumerate(parser.sample_headers):
        sample_duration_ms = (header.end - header.start) / header.sample_rate * 1000
        
        if sample_duration_ms < 50:  # Less than 50ms
            loop_info = parser.get_sample_loop_info(i)
            
            sample_info = {
                'id': i,
                'name': header.name,
                'duration_ms': sample_duration_ms,
                'sample_count': header.end - header.start,
                'sample_rate': header.sample_rate,
                'original_pitch': header.original_pitch,
                'loop_info': loop_info
            }
            
            if sample_duration_ms < 5:  # Very short (< 5ms)
                very_short_samples.append(sample_info)
            elif loop_info and loop_info['has_loop']:
                short_with_loops.append(sample_info)
            else:
                short_without_loops.append(sample_info)
    
    print(f"\nPROBLEMATIC SAMPLES ANALYSIS:")
    print(f"Very short samples (< 5ms): {len(very_short_samples)}")
    print(f"Short samples with loops (5-50ms): {len(short_with_loops)}")
    print(f"Short samples without loops (5-50ms): {len(short_without_loops)}")
    
    print(f"\nVERY SHORT SAMPLES (likely causing tapping):")
    for sample in very_short_samples[:10]:  # Show first 10
        loop_status = "HAS LOOPS" if sample['loop_info']['has_loop'] else "NO LOOPS"
        print(f"  {sample['name']}: {sample['duration_ms']:.1f}ms ({sample['sample_count']} samples) - {loop_status}")
        if sample['loop_info']['has_loop']:
            loop = sample['loop_info']
            print(f"    Loop: {loop['loop_start']}-{loop['loop_end']} ({loop['loop_length']} samples)")
    
    if len(very_short_samples) > 10:
        print(f"  ... and {len(very_short_samples) - 10} more")
    
    print(f"\nSHORT SAMPLES WITHOUT LOOPS (may cause tapping):")
    for sample in short_without_loops[:10]:  # Show first 10
        print(f"  {sample['name']}: {sample['duration_ms']:.1f}ms ({sample['sample_count']} samples)")
    
    if len(short_without_loops) > 10:
        print(f"  ... and {len(short_without_loops) - 10} more")
    
    # Test the loop extension logic on a few samples
    print(f"\nTESTING LOOP EXTENSION LOGIC:")
    
    test_samples = [s for s in very_short_samples + short_with_loops if s['loop_info']['has_loop']][:3]
    
    for sample in test_samples:
        print(f"\nTesting: {sample['name']} ({sample['duration_ms']:.1f}ms)")
        
        # Get the actual sample data
        sample_data = parser.get_sample(sample['id'])
        if sample_data is None:
            print("  ERROR: Could not load sample data")
            continue
        
        print(f"  Original length: {len(sample_data)} samples")
        print(f"  Loop info: {sample['loop_info']}")
        
        # Test if the loop extension would work
        loop_info = sample['loop_info']
        if loop_info['has_loop']:
            loop_start = loop_info['loop_start']
            loop_end = loop_info['loop_end']
            loop_length = loop_end - loop_start
            
            if loop_start < len(sample_data) and loop_end <= len(sample_data) and loop_length > 0:
                print(f"  ✓ Loop points are valid")
                
                # Simulate extending to 100ms
                target_samples = int(0.1 * sample['sample_rate'])
                if target_samples > len(sample_data):
                    attack_samples = loop_start
                    sustain_needed = target_samples - attack_samples
                    loop_repetitions = (sustain_needed // loop_length) + 1
                    
                    print(f"  ✓ Would extend from {len(sample_data)} to {target_samples} samples")
                    print(f"  ✓ Attack: {attack_samples}, Sustain: {sustain_needed}, Loops: {loop_repetitions}")
                else:
                    print(f"  - Sample is already long enough")
            else:
                print(f"  ✗ Invalid loop points!")
                print(f"    Sample length: {len(sample_data)}")
                print(f"    Loop start: {loop_start}, Loop end: {loop_end}")
        
    print(f"\n" + "="*60)
    print("RECOMMENDATIONS:")
    
    if very_short_samples:
        with_loops = [s for s in very_short_samples if s['loop_info']['has_loop']]
        without_loops = [s for s in very_short_samples if not s['loop_info']['has_loop']]
        
        print(f"1. Very short samples with loops ({len(with_loops)}): Should be handled by loop extension")
        print(f"2. Very short samples without loops ({len(without_loops)}): Will sound like tapping (expected for some percussion)")
        
        if with_loops:
            print(f"   → If these still sound like tapping, the loop extension logic needs debugging")
        
        if without_loops:
            print(f"   → These may be intentionally short (percussion hits, etc.)")
    
    print(f"3. Check if apply_pitch_bend_with_looping() is being called correctly")
    print(f"4. Verify that loop information is being passed properly to the playback system")

if __name__ == "__main__":
    debug_tapping_samples()
