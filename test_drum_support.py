#!/usr/bin/env python3
"""
Test script to validate drum and percussion support for channel 10.
"""

import os
import sys
from hexsf2newparser import SF2NewParser

def test_drum_detection(sf2_path: str):
    """Test drum kit detection in SF2 files."""
    print(f"Testing drum detection with: {sf2_path}")
    print("=" * 60)
    
    try:
        # Initialize and parse the SF2 file
        parser = SF2NewParser(sf2_path)
        parser.parse()
        
        print(f"✓ Successfully parsed SF2 file")
        print(f"  - Found {len(parser.presets)} presets")
        print(f"  - Found {len(parser.instruments)} instruments")
        print(f"  - Found {len(parser.sample_headers)} samples")
        print()
        
        # Test drum preset detection
        drum_preset = parser.find_drum_preset()
        if drum_preset is not None:
            preset = parser.presets[drum_preset]
            print(f"✓ Found drum kit: Preset {drum_preset}")
            print(f"  - Name: '{preset.name}'")
            print(f"  - Bank: {preset.bank_num}")
            print(f"  - Program: {preset.preset_num}")
            print(f"  - Zones: {preset.num_zones}")
        else:
            print("✗ No drum kit found in soundfont")
        print()
        
        # Show all presets with bank information
        print("All presets in soundfont:")
        for i, preset in enumerate(parser.presets):
            marker = " [DRUM]" if i == drum_preset else ""
            print(f"  {i:2d}: Bank {preset.bank_num:3d}, Program {preset.preset_num:3d} - '{preset.name}'{marker}")
        print()
        
        # Test GM drum mapping
        drum_mapping = parser.get_gm_drum_mapping()
        print(f"✓ GM drum mapping contains {len(drum_mapping)} drum sounds")
        print("Sample drum sounds:")
        sample_notes = [36, 38, 42, 46, 49, 51]  # Common drum sounds
        for note in sample_notes:
            if note in drum_mapping:
                print(f"  Note {note:2d}: {drum_mapping[note]}")
        print()
        
        # Test channel preset mapping with drums
        print("Testing channel preset mapping:")
        
        # Test default mapping (should detect drums on channel 10)
        default_mapping = parser.create_channel_preset_mapping()
        print("Default mapping:")
        for channel in range(16):
            preset_idx = default_mapping[channel]
            preset_name = parser.presets[preset_idx].name if preset_idx < len(parser.presets) else "Unknown"
            channel_type = "DRUMS" if channel == 9 else "MELODIC"
            print(f"  Channel {channel+1:2d} ({channel_type:7s}): Preset {preset_idx} - '{preset_name}'")
        print()
        
        # Test with explicit drum channel program
        test_programs = {i: 0 for i in range(16)}  # All piano
        test_programs[9] = -1  # Mark channel 10 as drums
        drum_mapping = parser.create_channel_preset_mapping(test_programs)
        print("Explicit drum channel mapping:")
        for channel in range(16):
            preset_idx = drum_mapping[channel]
            preset_name = parser.presets[preset_idx].name if preset_idx < len(parser.presets) else "Unknown"
            channel_type = "DRUMS" if channel == 9 else "MELODIC"
            print(f"  Channel {channel+1:2d} ({channel_type:7s}): Preset {preset_idx} - '{preset_name}'")
        print()
        
        # Test sample extraction for drum preset
        if drum_preset is not None:
            print("Testing drum sample extraction:")
            drum_samples = parser.get_samples_for_preset(drum_preset)
            if drum_samples:
                print(f"✓ Found {len(drum_samples)} drum samples")
                # Show some common drum notes
                common_drums = [36, 38, 42, 46, 49, 51]  # Kick, snare, hi-hat, etc.
                for note in common_drums:
                    if note in drum_samples:
                        sample_id, generators = drum_samples[note]
                        drum_name = drum_mapping.get(note, f"Note {note}")
                        print(f"  {drum_name}: Sample {sample_id}")
            else:
                print("✗ No drum samples found")
        
        print("\n" + "=" * 60)
        print("Drum detection test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Error during drum detection test: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """Main test function."""
    print("Drum and Percussion Support Test")
    print("=" * 60)
    
    # Try to find a soundfont file
    test_paths = [
        "test.sf2",
        "soundfont.sf2",
        "FluidR3_GM.sf2",
        "GeneralUser_GS.sf2"
    ]
    
    sf2_path = None
    for path in test_paths:
        if os.path.exists(path):
            sf2_path = path
            break
    
    if not sf2_path:
        print("No soundfont file found. Please provide a path:")
        sf2_path = input("Enter SF2 file path: ").strip()
        if not sf2_path or not os.path.exists(sf2_path):
            print("Invalid or missing soundfont file.")
            return False
    
    return test_drum_detection(sf2_path)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
