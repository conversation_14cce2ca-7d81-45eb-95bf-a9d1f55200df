#!/usr/bin/env python3
"""
Diagnose samples that sound like very short "tapping" sounds.
"""

import os
import sys
import numpy as np
from hexsf2newparser import SF2NewPars<PERSON>

def diagnose_tapping_samples(sf2_path: str):
    """Find and analyze samples that might sound like short taps."""
    print(f"Diagnosing tapping samples in: {sf2_path}")
    print("=" * 70)
    
    try:
        # Initialize and parse the SF2 file
        parser = SF2NewParser(sf2_path)
        parser.parse()
        parser.load_sample_data()
        
        print(f"✓ Loaded SF2 with {len(parser.sample_headers)} samples")
        print()
        
        # Analyze samples for potential "tapping" issues
        tapping_candidates = []
        total_samples = 0
        
        print("Analyzing samples for potential tapping issues...")
        print("-" * 70)
        
        for i, header in enumerate(parser.sample_headers[:100]):  # Check first 100 samples
            total_samples += 1
            
            # Calculate basic properties
            sample_length = header.end - header.start
            duration_seconds = sample_length / header.sample_rate if header.sample_rate > 0 else 0
            
            # Get actual sample data
            try:
                sample_data = parser.get_raw_sample_data(i)
                if sample_data is None:
                    continue
                    
                actual_length = len(sample_data)
                actual_duration = actual_length / header.sample_rate if header.sample_rate > 0 else 0
                
                # Calculate audio properties
                max_amplitude = np.max(np.abs(sample_data)) if len(sample_data) > 0 else 0
                rms_amplitude = np.sqrt(np.mean(sample_data**2)) if len(sample_data) > 0 else 0
                
                # Check for loop information
                loop_info = parser.get_sample_loop_info(i)
                has_loop = loop_info['has_loop'] if loop_info else False
                loop_length = loop_info['loop_length'] if loop_info and has_loop else 0
                
                # Identify potential tapping issues
                issues = []
                
                # Issue 1: Very short duration (less than 100ms)
                if actual_duration < 0.1 and actual_duration > 0:
                    issues.append(f"Very short ({actual_duration*1000:.1f}ms)")
                
                # Issue 2: Very low amplitude (might be barely audible)
                if max_amplitude < 0.01:
                    issues.append(f"Very quiet (max: {max_amplitude:.4f})")
                
                # Issue 3: Extremely short samples (less than 1000 samples)
                if actual_length < 1000:
                    issues.append(f"Tiny sample ({actual_length} samples)")
                
                # Issue 4: Header vs actual length mismatch
                if abs(sample_length - actual_length) > 100:
                    issues.append(f"Length mismatch (header: {sample_length}, actual: {actual_length})")
                
                # Issue 5: Loop issues
                if has_loop and loop_length < 100:
                    issues.append(f"Tiny loop ({loop_length} samples)")
                elif not has_loop and actual_duration < 0.5:
                    issues.append("No loop + short duration")
                
                # If any issues found, add to candidates
                if issues:
                    tapping_candidates.append({
                        'id': i,
                        'name': header.name,
                        'sample_length': sample_length,
                        'actual_length': actual_length,
                        'duration': actual_duration,
                        'max_amplitude': max_amplitude,
                        'rms_amplitude': rms_amplitude,
                        'sample_rate': header.sample_rate,
                        'has_loop': has_loop,
                        'loop_length': loop_length,
                        'issues': issues,
                        'sample_type': header.sample_type,
                        'original_pitch': header.original_pitch
                    })
                
                # Show progress for first few samples
                if i < 10:
                    status = "ISSUES: " + ", ".join(issues) if issues else "OK"
                    print(f"Sample {i:2d}: '{header.name[:25]:25s}' - "
                          f"{actual_length:5d} samples ({actual_duration*1000:5.1f}ms) "
                          f"amp:{max_amplitude:.3f} - {status}")
            
            except Exception as e:
                print(f"Error processing sample {i}: {e}")
                continue
        
        print()
        print("=" * 70)
        print(f"ANALYSIS RESULTS")
        print("=" * 70)
        
        print(f"Total samples analyzed: {total_samples}")
        print(f"Samples with potential tapping issues: {len(tapping_candidates)}")
        print()
        
        if tapping_candidates:
            print("TOP TAPPING CANDIDATES:")
            print("-" * 70)
            
            # Sort by severity (more issues = higher priority)
            tapping_candidates.sort(key=lambda x: len(x['issues']), reverse=True)
            
            for i, candidate in enumerate(tapping_candidates[:20]):  # Show top 20
                print(f"{i+1:2d}. Sample {candidate['id']:3d}: '{candidate['name'][:30]:30s}'")
                print(f"    Duration: {candidate['duration']*1000:6.1f}ms, "
                      f"Amplitude: {candidate['max_amplitude']:.4f}, "
                      f"Loop: {'Yes' if candidate['has_loop'] else 'No'}")
                print(f"    Issues: {', '.join(candidate['issues'])}")
                print()
        
        # Recommendations
        print("RECOMMENDATIONS:")
        print("-" * 70)
        
        very_short = len([c for c in tapping_candidates if c['duration'] < 0.1])
        very_quiet = len([c for c in tapping_candidates if c['max_amplitude'] < 0.01])
        no_loop_short = len([c for c in tapping_candidates if not c['has_loop'] and c['duration'] < 0.5])
        
        if very_short > 0:
            print(f"• {very_short} samples are very short (<100ms) - these will sound like taps")
        
        if very_quiet > 0:
            print(f"• {very_quiet} samples are very quiet (<0.01 amplitude) - barely audible")
        
        if no_loop_short > 0:
            print(f"• {no_loop_short} samples have no loop and are short - will cut off quickly")
        
        print()
        print("POTENTIAL FIXES:")
        print("1. Extend very short samples by repeating or looping")
        print("2. Boost amplitude of very quiet samples")
        print("3. Add artificial loops to short non-looped samples")
        print("4. Filter out samples shorter than a minimum threshold")
        
        return len(tapping_candidates) == 0
        
    except Exception as e:
        print(f"✗ Error during diagnosis: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """Main diagnostic function."""
    # Try to find a soundfont
    test_paths = [
        "arachno.sf2",
        "Arachno.sf2", 
        "arachno-soundfont.sf2",
        "Arachno SoundFont - Version 1.0.sf2"
    ]
    
    sf2_path = None
    for path in test_paths:
        if os.path.exists(path):
            sf2_path = path
            break
    
    if not sf2_path:
        print("Soundfont not found. Please provide a path:")
        sf2_path = input("Enter SF2 file path: ").strip()
        if not sf2_path or not os.path.exists(sf2_path):
            print("Invalid or missing soundfont file.")
            return False
    
    return diagnose_tapping_samples(sf2_path)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
