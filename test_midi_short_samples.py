#!/usr/bin/env python3
"""
Test MIDI processing with short samples to see if they're being handled correctly.
"""

import json
import numpy as np
import sys
import os

# Add the current directory to path
sys.path.append(os.path.dirname(__file__))

def test_midi_short_samples():
    """Test MIDI processing with short samples."""
    
    # Load settings
    with open('hexsyn_settings.json', 'r') as f:
        settings = json.load(f)
    
    # Create a simple test MIDI file with a single note
    import mido
    
    # Create a simple MIDI file
    mid = mido.MidiFile()
    track = mido.MidiTrack()
    mid.tracks.append(track)
    
    # Set tempo (120 BPM)
    track.append(mido.MetaMessage('set_tempo', tempo=500000, time=0))
    
    # Program change to use a specific instrument that might have short samples
    # Let's try program 7 (Clavinet) which we know has short samples
    track.append(mido.Message('program_change', channel=0, program=7, time=0))
    
    # Note on
    track.append(mido.Message('note_on', channel=0, note=60, velocity=100, time=0))
    
    # Note off after 1 second (480 ticks at 480 ticks per beat, 120 BPM)
    track.append(mido.Message('note_off', channel=0, note=60, velocity=0, time=480))
    
    # Save the test MIDI file
    test_midi_path = 'test_short_sample.mid'
    mid.save(test_midi_path)
    
    print(f"Created test MIDI file: {test_midi_path}")
    print("Program 7 (Clavinet) - Note 60 (C4) - 1 second duration")
    
    # Now try to process it with the main script
    print("\nTesting MIDI processing...")
    
    # Import the main script functions
    try:
        # We'll just run the main script with our test file
        import subprocess
        
        # Update settings to use our test file
        settings['midi_path'] = test_midi_path
        settings['output_path'] = 'test_short_sample_output.mp3'
        
        with open('hexsyn_settings.json', 'w') as f:
            json.dump(settings, f, indent=2)
        
        print("Updated settings to use test MIDI file")
        print("You can now run the main script to test if short samples work correctly")
        print("Look for any error messages about extremely short samples")
        
    except Exception as e:
        print(f"Error setting up test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_midi_short_samples()
