#!/usr/bin/env python3
"""
Simple test to play a single Clavinet note and see if loop extension works
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import json
import mido
import numpy as np
from hexsf2newparser import SF2New<PERSON>ars<PERSON>

def create_simple_test_midi():
    """Create a simple MIDI file with one long Clavinet note"""
    mid = mido.MidiFile()
    track = mido.MidiTrack()
    mid.tracks.append(track)
    
    # Set tempo (120 BPM)
    track.append(mido.MetaMessage('set_tempo', tempo=500000, time=0))
    
    # Program change to Clavinet (program 7)
    track.append(mido.Message('program_change', channel=0, program=7, time=0))
    
    # Note on C4 (note 60) for 2 seconds
    track.append(mido.Message('note_on', channel=0, note=60, velocity=100, time=0))
    track.append(mido.Message('note_off', channel=0, note=60, velocity=0, time=int(2.0 * 480)))  # 2 seconds at 480 ticks per beat
    
    mid.save('test_clavinet.mid')
    print("Created test_clavinet.mid")

def test_clavinet_sample():
    """Test the Clavinet sample directly"""
    
    # Load settings
    with open('hexsyn_settings.json', 'r') as f:
        settings = json.load(f)
    
    sf2_path = settings['sf2_path']
    
    print(f"Testing Clavinet sample with SF2: {sf2_path}")
    
    # Load SF2 parser
    try:
        parser = SF2NewParser(sf2_path)
        print(f"SF2 parser loaded successfully")
        print(f"Sample headers available: {hasattr(parser, 'sample_headers')}")
        if hasattr(parser, 'sample_headers'):
            print(f"Number of sample headers: {len(parser.sample_headers) if parser.sample_headers else 0}")
    except Exception as e:
        print(f"Error loading SF2 parser: {e}")
        return
    
    # Find any short samples that might be causing issues
    short_samples = []
    all_samples = []
    for i, header in enumerate(parser.sample_headers):
        sample_length = header.end - header.start
        all_samples.append((i, header.name, sample_length))
        if sample_length < 2205:  # Less than 50ms at 44100Hz
            short_samples.append((i, header.name, sample_length))

    print(f"Total samples: {len(all_samples)}")
    print(f"Found {len(short_samples)} short samples:")

    if short_samples:
        for i, (sample_id, name, length) in enumerate(short_samples[:10]):  # Show first 10
            print(f"  {sample_id}: {name} - {length} samples ({length/44100*1000:.1f}ms)")
    else:
        print("No short samples found. Showing first 10 samples:")
        for i, (sample_id, name, length) in enumerate(all_samples[:10]):
            print(f"  {sample_id}: {name} - {length} samples ({length/44100*1000:.1f}ms)")
        return

    # Use the first short sample for testing
    clavinet_sample_id = short_samples[0][0]
    
    # Get sample and loop info
    sample_data, sample_rate, loop_info = parser.get_sample(clavinet_sample_id)
    
    print(f"\nClavinet sample info:")
    print(f"  Length: {len(sample_data)} samples ({len(sample_data)/sample_rate*1000:.1f}ms)")
    print(f"  Sample rate: {sample_rate}")
    print(f"  Loop info: {loop_info}")
    
    if loop_info and loop_info.get('has_loop'):
        print(f"  Loop start: {loop_info['loop_start']}")
        print(f"  Loop end: {loop_info['loop_end']}")
        print(f"  Loop length: {loop_info['loop_length']}")
        
        # Test loop extension manually
        loop_start = loop_info['loop_start']
        loop_end = loop_info['loop_end']
        
        if loop_start < len(sample_data) and loop_end <= len(sample_data):
            attack_section = sample_data[:loop_start]
            loop_section = sample_data[loop_start:loop_end]
            
            print(f"  Attack section: {len(attack_section)} samples")
            print(f"  Loop section: {len(loop_section)} samples")
            
            # Extend to 100ms (4410 samples at 44100Hz)
            target_length = 4410
            if len(sample_data) < target_length:
                extended_sample = np.copy(attack_section)
                
                # Add loop repetitions
                while len(extended_sample) < target_length:
                    remaining = target_length - len(extended_sample)
                    if remaining >= len(loop_section):
                        extended_sample = np.concatenate([extended_sample, loop_section])
                    else:
                        extended_sample = np.concatenate([extended_sample, loop_section[:remaining]])
                
                print(f"  Extended sample: {len(extended_sample)} samples ({len(extended_sample)/sample_rate*1000:.1f}ms)")
                print(f"  Extension successful: {len(extended_sample) >= target_length}")
    else:
        print("  No loop information available")

if __name__ == "__main__":
    create_simple_test_midi()
    test_clavinet_sample()
