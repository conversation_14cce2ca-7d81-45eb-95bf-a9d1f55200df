#!/usr/bin/env python3
"""
Test script to verify that loop information is being preserved correctly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hexsf2newparser import SF2NewParser
import json

def test_loop_info_preservation():
    """Test that loop information is preserved through the sample loading process"""
    
    # Load settings
    with open('hexsyn_settings.json', 'r') as f:
        settings = json.load(f)
    
    sf2_path = settings['sf2_path']
    
    print(f"Testing loop info preservation with SF2: {sf2_path}")
    
    # Load SF2 parser
    parser = SF2NewParser(sf2_path)
    
    # Test a few known short samples that should have loops
    test_samples = [
        ("Clavinet D6", 160),  # Known short sample with loops
        ("DrawBar Organ C#10", 13),  # Another known short sample
    ]
    
    print("\n=== Testing individual sample loop info ===")
    for sample_name, expected_length in test_samples:
        # Find sample by name
        sample_id = None
        for i, header in enumerate(parser.sample_headers):
            if sample_name.lower() in header.name.lower():
                sample_id = i
                break
        
        if sample_id is not None:
            # Get loop info directly from parser
            loop_info = parser.get_sample_loop_info(sample_id)
            sample_data, sample_rate, returned_loop_info = parser.get_sample(sample_id)
            
            print(f"\nSample: {sample_name} (ID: {sample_id})")
            print(f"  Length: {len(sample_data)} samples")
            print(f"  Direct loop info: {loop_info}")
            print(f"  Returned loop info: {returned_loop_info}")
            print(f"  Loop info match: {loop_info == returned_loop_info}")
        else:
            print(f"Sample '{sample_name}' not found")
    
    print("\n=== Testing sample loading process ===")
    
    # Import the load_samples function
    from importlib import import_module
    main_module = import_module("midi-to-audio-with-correct-bpm - Copy (3) - Copy")
    
    # Load samples using the main function
    samples = main_module.load_samples(sf2_path, None, False, 0.2, 44100)
    
    # Check a few samples for loop info
    short_samples_with_loops = 0
    short_samples_without_loops = 0
    
    for (channel, note), (sample_data, sample_rate, loop_info) in samples.items():
        if len(sample_data) < 2205:  # Less than 50ms at 44100Hz
            if loop_info and loop_info.get('has_loop'):
                short_samples_with_loops += 1
                if short_samples_with_loops <= 3:  # Show first 3 examples
                    print(f"  Channel {channel}, Note {note}: {len(sample_data)} samples, loop_info: {loop_info}")
            else:
                short_samples_without_loops += 1
    
    print(f"\nResults from load_samples():")
    print(f"  Short samples WITH loops: {short_samples_with_loops}")
    print(f"  Short samples WITHOUT loops: {short_samples_without_loops}")
    
    if short_samples_with_loops > 0:
        print("✅ SUCCESS: Loop information is being preserved!")
    else:
        print("❌ FAILURE: Loop information is still being lost!")

if __name__ == "__main__":
    test_loop_info_preservation()
