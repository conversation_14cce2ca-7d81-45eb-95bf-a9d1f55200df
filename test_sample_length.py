#!/usr/bin/env python3
"""
Test script to verify that samples are not being cut off prematurely.
"""

import os
import sys
import numpy as np
from hexsf2newparser import SF2NewParser

def test_sample_lengths(sf2_path: str):
    """Test that samples have reasonable lengths and aren't truncated."""
    print(f"Testing sample lengths with: {sf2_path}")
    print("=" * 60)
    
    try:
        # Initialize and parse the SF2 file
        parser = SF2NewParser(sf2_path)
        parser.parse()
        
        print(f"✓ Successfully parsed SF2 file")
        print(f"  - Found {len(parser.presets)} presets")
        print(f"  - Found {len(parser.sample_headers)} samples")
        print()
        
        # Test sample lengths
        short_samples = []
        total_samples = 0
        valid_samples = 0
        
        print("Analyzing sample lengths...")
        for i, header in enumerate(parser.sample_headers):
            sample_length = header.end - header.start
            duration_seconds = sample_length / header.sample_rate if header.sample_rate > 0 else 0
            
            total_samples += 1
            
            if sample_length > 0:
                valid_samples += 1
                
                # Check for very short samples (less than 0.1 seconds)
                if duration_seconds < 0.1 and duration_seconds > 0:
                    short_samples.append({
                        'id': i,
                        'name': header.name,
                        'length': sample_length,
                        'duration': duration_seconds,
                        'sample_rate': header.sample_rate
                    })
                
                # Show some sample info for first few samples
                if i < 10:
                    print(f"  Sample {i:2d}: '{header.name[:20]:20s}' - "
                          f"{sample_length:6d} samples ({duration_seconds:.3f}s) @ {header.sample_rate}Hz")
        
        print(f"\nSample Analysis:")
        print(f"  Total samples: {total_samples}")
        print(f"  Valid samples: {valid_samples}")
        print(f"  Short samples (< 0.1s): {len(short_samples)}")
        
        if short_samples:
            print(f"\nWarning: Found {len(short_samples)} very short samples:")
            for sample in short_samples[:5]:  # Show first 5
                print(f"  - {sample['name']}: {sample['length']} samples ({sample['duration']:.3f}s)")
            if len(short_samples) > 5:
                print(f"  ... and {len(short_samples) - 5} more")
        
        # Test actual sample extraction
        print(f"\nTesting sample extraction...")
        extraction_success = 0
        extraction_errors = 0
        
        for i in range(min(20, len(parser.sample_headers))):  # Test first 20 samples
            try:
                sample_data = parser.get_sample(i)
                if sample_data is not None:
                    extraction_success += 1
                    header = parser.sample_headers[i]
                    expected_length = header.end - header.start
                    actual_length = len(sample_data)
                    
                    if i < 5:  # Show details for first 5
                        print(f"  Sample {i}: Expected {expected_length}, got {actual_length} samples")
                        
                    # Check for significant length discrepancies
                    if abs(actual_length - expected_length) > expected_length * 0.1:  # More than 10% difference
                        print(f"  Warning: Sample {i} length mismatch - expected {expected_length}, got {actual_length}")
                else:
                    extraction_errors += 1
                    print(f"  Error: Could not extract sample {i}")
            except Exception as e:
                extraction_errors += 1
                print(f"  Error extracting sample {i}: {e}")
        
        print(f"\nExtraction Results:")
        print(f"  Successful extractions: {extraction_success}")
        print(f"  Failed extractions: {extraction_errors}")
        
        # Test preset sample mapping
        if len(parser.presets) > 0:
            print(f"\nTesting preset sample mapping...")
            preset_idx = 0
            preset = parser.presets[preset_idx]
            print(f"Testing preset 0: '{preset.name}'")
            
            sample_map = parser.get_samples_for_preset(preset_idx)
            if sample_map:
                print(f"  Found {len(sample_map)} note mappings")
                
                # Test a few specific notes
                test_notes = [60, 64, 67, 72]  # C4, E4, G4, C5
                for note in test_notes:
                    if note in sample_map:
                        sample_id, generators = sample_map[note]
                        sample_data = parser.get_sample(sample_id)
                        loop_info = parser.get_sample_loop_info(sample_id)

                        if sample_data is not None:
                            duration = len(sample_data) / parser.sample_headers[sample_id].sample_rate
                            loop_str = ""
                            if loop_info and loop_info['has_loop']:
                                loop_duration = loop_info['loop_length'] / parser.sample_headers[sample_id].sample_rate
                                loop_str = f" (loop: {loop_info['loop_start']}-{loop_info['loop_end']}, {loop_duration:.3f}s)"
                            print(f"  Note {note}: Sample {sample_id}, {len(sample_data)} samples ({duration:.3f}s){loop_str}")
                        else:
                            print(f"  Note {note}: Sample {sample_id} - extraction failed")
            else:
                print(f"  No sample mappings found for preset")
        
        print("\n" + "=" * 60)
        print("Sample length test completed!")
        
        # Summary
        if len(short_samples) > len(parser.sample_headers) * 0.5:
            print("⚠ Warning: Many samples appear to be very short. This may indicate a parsing issue.")
        elif extraction_errors > extraction_success * 0.1:
            print("⚠ Warning: High extraction error rate. Check SF2 file integrity.")
        else:
            print("✓ Sample lengths appear reasonable.")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during sample length test: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """Main test function."""
    print("Sample Length Test")
    print("=" * 60)
    
    # Try to find a soundfont file
    test_paths = [
        "test.sf2",
        "soundfont.sf2",
        "FluidR3_GM.sf2",
        "GeneralUser_GS.sf2"
    ]
    
    sf2_path = None
    for path in test_paths:
        if os.path.exists(path):
            sf2_path = path
            break
    
    if not sf2_path:
        print("No soundfont file found. Please provide a path:")
        sf2_path = input("Enter SF2 file path: ").strip()
        if not sf2_path or not os.path.exists(sf2_path):
            print("Invalid or missing soundfont file.")
            return False
    
    return test_sample_lengths(sf2_path)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
