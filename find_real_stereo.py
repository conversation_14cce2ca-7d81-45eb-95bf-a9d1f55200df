#!/usr/bin/env python3
"""
Find real stereo samples in the Arachno soundfont.
"""

import os
import sys
import numpy as np
from hexsf2newparser import SF2NewParser

def find_real_stereo_samples(sf2_path: str):
    """Find samples that are actually stereo pairs."""
    print(f"Analyzing stereo samples in: {sf2_path}")
    print("=" * 60)
    
    try:
        # Initialize and parse the SF2 file
        parser = SF2NewParser(sf2_path)
        parser.parse()
        parser.load_sample_data()
        
        print(f"✓ Loaded SF2 with {len(parser.sample_headers)} samples")
        
        # Find all left channel samples
        left_samples = []
        right_samples = []
        mono_samples = []
        
        for i, header in enumerate(parser.sample_headers):
            if header.sample_type == 4:  # Left channel
                left_samples.append(i)
            elif header.sample_type == 2:  # Right channel
                right_samples.append(i)
            elif header.sample_type == 1:  # Mono
                mono_samples.append(i)
        
        print(f"Found {len(left_samples)} left samples, {len(right_samples)} right samples, {len(mono_samples)} mono samples")
        
        # Check for proper stereo pairs
        real_stereo_pairs = []
        broken_links = []
        
        for left_id in left_samples[:20]:  # Check first 20 left samples
            header = parser.sample_headers[left_id]
            right_id = header.sample_link
            
            if right_id >= len(parser.sample_headers):
                broken_links.append((left_id, right_id, "invalid_id"))
                continue
            
            right_header = parser.sample_headers[right_id]
            
            if right_header.sample_type == 2:  # Right channel
                if right_header.sample_link == left_id:  # Links back
                    # Test if they're actually different
                    left_data = parser.get_raw_sample_data(left_id)
                    right_data = parser.get_raw_sample_data(right_id)
                    
                    if left_data is not None and right_data is not None:
                        min_length = min(len(left_data), len(right_data))
                        if min_length > 100:
                            correlation = np.corrcoef(left_data[:min_length], right_data[:min_length])[0, 1]
                            if not np.isnan(correlation) and correlation < 0.95:
                                real_stereo_pairs.append((left_id, right_id, correlation))
                                print(f"✓ Real stereo pair: {left_id} ('{header.name}') <-> {right_id} ('{right_header.name}'), corr={correlation:.3f}")
                            else:
                                broken_links.append((left_id, right_id, f"too_similar_corr_{correlation:.3f}"))
                        else:
                            broken_links.append((left_id, right_id, "too_short"))
                    else:
                        broken_links.append((left_id, right_id, "data_load_failed"))
                else:
                    broken_links.append((left_id, right_id, f"no_backlink_{right_header.sample_link}"))
            else:
                broken_links.append((left_id, right_id, f"wrong_type_{right_header.sample_type}"))
        
        print(f"\nFound {len(real_stereo_pairs)} real stereo pairs")
        print(f"Found {len(broken_links)} broken stereo links")
        
        if broken_links:
            print(f"\nBroken link examples:")
            for left_id, right_id, reason in broken_links[:5]:
                left_header = parser.sample_headers[left_id]
                print(f"  {left_id} ('{left_header.name}') -> {right_id}: {reason}")
        
        # Test some presets to see which ones might have real stereo
        print(f"\nTesting presets for stereo content:")
        for preset_idx in range(min(10, len(parser.presets))):
            preset = parser.presets[preset_idx]
            sample_map = parser.get_samples_for_preset(preset_idx)
            
            stereo_notes = 0
            total_notes = 0
            
            for note in range(21, 109):  # Piano range
                if note in sample_map:
                    total_notes += 1
                    sample_id, _ = sample_map[note]
                    header = parser.sample_headers[sample_id]
                    
                    # Check if this sample would produce real stereo
                    if header.sample_type == 4:  # Left channel
                        for left_id, right_id, corr in real_stereo_pairs:
                            if left_id == sample_id:
                                stereo_notes += 1
                                break
            
            if total_notes > 0:
                stereo_ratio = stereo_notes / total_notes
                print(f"  Preset {preset_idx} ('{preset.name}'): {stereo_notes}/{total_notes} stereo notes ({stereo_ratio:.1%})")
        
        return len(real_stereo_pairs) > 0
        
    except Exception as e:
        print(f"✗ Error during analysis: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """Main function."""
    # Try to find Arachno soundfont
    test_paths = [
        "arachno.sf2",
        "Arachno.sf2", 
        "arachno-soundfont.sf2",
        "Arachno SoundFont - Version 1.0.sf2"
    ]
    
    sf2_path = None
    for path in test_paths:
        if os.path.exists(path):
            sf2_path = path
            break
    
    if not sf2_path:
        print("Arachno soundfont not found. Please provide a path:")
        sf2_path = input("Enter SF2 file path: ").strip()
        if not sf2_path or not os.path.exists(sf2_path):
            print("Invalid or missing soundfont file.")
            return False
    
    return find_real_stereo_samples(sf2_path)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
