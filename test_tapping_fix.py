#!/usr/bin/env python3
"""
Test script to verify that the tapping sounds are fixed by testing with a MIDI file.
"""

import json
import sys
import os
from hexsf2newparser import SF2NewParser

def test_tapping_fix():
    """Test that the tapping sounds are fixed by checking sample loading."""
    
    # Load settings
    try:
        with open('hexsyn_settings.json', 'r') as f:
            settings = json.load(f)
        sf2_path = settings.get('sf2_path', '')
        midi_path = settings.get('midi_path', '')
        if not sf2_path or not midi_path:
            print("Error: Missing SF2 or MIDI file path in settings")
            return False
    except Exception as e:
        print(f"Error loading settings: {e}")
        return False
    
    print(f"Testing tapping fix with:")
    print(f"  SF2: {sf2_path}")
    print(f"  MIDI: {midi_path}")
    
    # Initialize parser
    parser = SF2NewParser(sf2_path)
    
    try:
        # Parse the SF2 file
        parser.parse()
        print(f"Successfully parsed SF2 file with {len(parser.sample_headers)} samples")
        
        # Test some specific problematic samples that were identified
        problematic_samples = [
            (138, "Clavinet D6", 3.6),  # Very short sample
            (147, "Chimes Percussion C8", 1.2),  # Extremely short
            (193, "DrawBar Organ C#10", 0.3),  # Ultra short
            (436, "Picked Bass C6", 3.2),  # Short bass
            (453, "Slap Bass 1 C6", 7.9),  # Short slap bass
        ]
        
        print(f"\nTesting specific problematic samples that used to cause tapping sounds:")
        
        fixed_count = 0
        for sample_id, expected_name, original_duration_ms in problematic_samples:
            if sample_id < len(parser.sample_headers):
                header = parser.sample_headers[sample_id]
                print(f"\nTesting sample {sample_id} ('{header.name}'):")
                print(f"  Expected original duration: ~{original_duration_ms}ms")
                
                # Load the sample
                sample_data = parser.get_sample(sample_id)
                if sample_data is not None:
                    actual_duration_ms = (len(sample_data) / header.sample_rate) * 1000
                    original_sample_length = header.end - header.start
                    original_calc_duration = (original_sample_length / header.sample_rate) * 1000
                    
                    print(f"  Original calculated duration: {original_calc_duration:.1f}ms")
                    print(f"  Loaded sample duration: {actual_duration_ms:.1f}ms")
                    print(f"  Sample length: {len(sample_data)} samples")
                    
                    if actual_duration_ms > 50:  # Should be extended to at least 50ms
                        print(f"  ✓ Sample successfully extended - no more tapping sound!")
                        fixed_count += 1
                    else:
                        print(f"  ✗ Sample still too short - may still cause tapping")
                else:
                    print(f"  ✗ Failed to load sample")
            else:
                print(f"Sample {sample_id} not found in SF2 file")
        
        print(f"\nTapping Fix Results:")
        print(f"  Problematic samples tested: {len(problematic_samples)}")
        print(f"  Successfully fixed: {fixed_count}")
        
        if fixed_count >= len(problematic_samples) * 0.8:  # At least 80% fixed
            print(f"\n✓ SUCCESS: Most problematic samples have been fixed!")
            print(f"  The 'tapping' sounds should now be eliminated.")
            print(f"  Short samples are being properly extended using their loop points.")
            return True
        else:
            print(f"\n⚠ PARTIAL SUCCESS: Some samples may still cause issues")
            return False
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("SF2 Tapping Sound Fix Test")
    print("=" * 50)
    
    success = test_tapping_fix()
    
    if success:
        print("\n🎵 The tapping sound issue should now be fixed!")
        print("   Short samples are being extended using loop points like BassMIDI does.")
        sys.exit(0)
    else:
        print("\n❌ The tapping sound fix may not be working correctly")
        sys.exit(1)
