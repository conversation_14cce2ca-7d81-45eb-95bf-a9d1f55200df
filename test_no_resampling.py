#!/usr/bin/env python3
"""
Test the performance improvement from removing sample resampling.
"""

import os
import sys
import time
from hexsf2newparser import SF2New<PERSON>ars<PERSON>

def test_sample_loading_performance(sf2_path: str):
    """Test how fast sample loading is without resampling."""
    print(f"Testing sample loading performance with: {sf2_path}")
    print("=" * 60)
    
    try:
        # Initialize and parse the SF2 file
        start_time = time.time()
        parser = SF2NewParser(sf2_path)
        parser.parse()
        parse_time = time.time() - start_time
        print(f"✓ SF2 parsing took: {parse_time:.2f} seconds")
        
        # Load sample data
        start_time = time.time()
        parser.load_sample_data()
        load_time = time.time() - start_time
        print(f"✓ Sample data loading took: {load_time:.2f} seconds")
        
        print(f"✓ Loaded SF2 with {len(parser.sample_headers)} samples")
        
        # Test sample extraction speed
        start_time = time.time()
        samples_tested = 0
        
        for preset_idx in range(min(5, len(parser.presets))):  # Test first 5 presets
            sample_map = parser.get_samples_for_preset(preset_idx)
            
            for note in range(21, 109, 12):  # Test every octave
                if note in sample_map:
                    sample_id, _ = sample_map[note]
                    
                    # Test both mono and stereo extraction
                    mono_sample = parser.get_sample(sample_id)
                    stereo_sample = parser.get_stereo_sample(sample_id)
                    
                    if mono_sample is not None and stereo_sample is not None:
                        samples_tested += 1
        
        extraction_time = time.time() - start_time
        print(f"✓ Sample extraction for {samples_tested} samples took: {extraction_time:.2f} seconds")
        print(f"✓ Average extraction time per sample: {extraction_time/samples_tested:.4f} seconds")
        
        # Test different sample rates
        sample_rates = {}
        for header in parser.sample_headers[:100]:  # Check first 100 samples
            rate = header.sample_rate
            if rate not in sample_rates:
                sample_rates[rate] = 0
            sample_rates[rate] += 1
        
        print(f"\nSample rate distribution (first 100 samples):")
        for rate, count in sorted(sample_rates.items()):
            print(f"  {rate} Hz: {count} samples")
        
        total_time = parse_time + load_time + extraction_time
        print(f"\n✓ Total time: {total_time:.2f} seconds")
        print(f"✓ No resampling performed - samples will be converted during playback for much better performance!")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during test: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """Main test function."""
    # Try to find a soundfont
    test_paths = [
        "arachno.sf2",
        "Arachno.sf2", 
        "arachno-soundfont.sf2",
        "Arachno SoundFont - Version 1.0.sf2"
    ]
    
    sf2_path = None
    for path in test_paths:
        if os.path.exists(path):
            sf2_path = path
            break
    
    if not sf2_path:
        print("Soundfont not found. Please provide a path:")
        sf2_path = input("Enter SF2 file path: ").strip()
        if not sf2_path or not os.path.exists(sf2_path):
            print("Invalid or missing soundfont file.")
            return False
    
    return test_sample_loading_performance(sf2_path)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
