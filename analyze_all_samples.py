#!/usr/bin/env python3
"""
Comprehensive SF2 sample analysis to find problematic samples.
"""

import os
import sys
import numpy as np
from hexsf2newparser import <PERSON>2<PERSON>ew<PERSON>arser

def analyze_sample_quality(sample_data, sample_id, header):
    """Analyze a single sample for quality issues."""
    issues = []
    
    if sample_data is None:
        return ["NULL_SAMPLE"]
    
    # Convert to mono for analysis if stereo
    if sample_data.ndim == 2:
        mono_data = np.mean(sample_data, axis=1)
    else:
        mono_data = sample_data
    
    length = len(mono_data)
    duration_ms = (length / header.sample_rate) * 1000 if header.sample_rate > 0 else 0
    
    # Check for extremely short samples (likely "taps")
    if duration_ms < 10:  # Less than 10ms
        issues.append(f"VERY_SHORT_{duration_ms:.1f}ms")
    elif duration_ms < 50:  # Less than 50ms
        issues.append(f"SHORT_{duration_ms:.1f}ms")
    
    # Check for very quiet samples
    max_amplitude = np.max(np.abs(mono_data))
    if max_amplitude < 0.001:  # Very quiet
        issues.append(f"VERY_QUIET_{max_amplitude:.6f}")
    elif max_amplitude < 0.01:  # Quiet
        issues.append(f"QUIET_{max_amplitude:.4f}")
    
    # Check for mostly silence
    non_zero_samples = np.count_nonzero(np.abs(mono_data) > 0.001)
    silence_ratio = 1.0 - (non_zero_samples / length)
    if silence_ratio > 0.95:  # More than 95% silence
        issues.append(f"MOSTLY_SILENT_{silence_ratio:.2f}")
    
    # Check for clipping
    if max_amplitude > 0.99:
        issues.append("CLIPPED")
    
    # Check for DC offset
    dc_offset = np.mean(mono_data)
    if abs(dc_offset) > 0.1:
        issues.append(f"DC_OFFSET_{dc_offset:.3f}")
    
    return issues

def analyze_all_samples(sf2_path: str):
    """Analyze all samples in the SF2 file."""
    print(f"Analyzing all samples in: {sf2_path}")
    print("=" * 80)
    
    try:
        # Initialize and parse the SF2 file
        parser = SF2NewParser(sf2_path)
        parser.parse()
        parser.load_sample_data()
        
        print(f"✓ Loaded SF2 with {len(parser.sample_headers)} samples")
        print()
        
        # Statistics
        total_samples = len(parser.sample_headers)
        problematic_samples = []
        very_short_samples = []
        quiet_samples = []
        good_samples = []
        
        # Analyze each sample
        print("Sample Analysis:")
        print("-" * 80)
        print(f"{'ID':<4} {'Name':<25} {'Type':<6} {'Rate':<6} {'Length':<8} {'Duration':<10} {'Issues'}")
        print("-" * 80)
        
        for i, header in enumerate(parser.sample_headers):
            if i >= total_samples - 1:  # Skip terminator
                break
                
            # Get sample data
            sample_data = parser.get_sample(i)
            issues = analyze_sample_quality(sample_data, i, header)
            
            # Calculate basic info
            if sample_data is not None:
                if sample_data.ndim == 2:
                    length = len(sample_data)
                else:
                    length = len(sample_data)
                duration_ms = (length / header.sample_rate) * 1000 if header.sample_rate > 0 else 0
            else:
                length = 0
                duration_ms = 0
            
            # Sample type description
            type_desc = "MONO"
            if header.sample_type & 2:  # Right channel
                type_desc = "RIGHT"
            elif header.sample_type & 4:  # Left channel  
                type_desc = "LEFT"
            elif header.sample_type & 8:  # Linked
                type_desc = "LINK"
            
            # Categorize sample
            if issues:
                problematic_samples.append((i, header.name, issues))
                if any("SHORT" in issue or "VERY_SHORT" in issue for issue in issues):
                    very_short_samples.append((i, header.name, duration_ms))
                if any("QUIET" in issue or "VERY_QUIET" in issue for issue in issues):
                    quiet_samples.append((i, header.name))
            else:
                good_samples.append((i, header.name))
            
            # Print sample info
            issues_str = ", ".join(issues) if issues else "OK"
            print(f"{i:<4} {header.name[:24]:<25} {type_desc:<6} {header.sample_rate:<6} {length:<8} {duration_ms:<10.1f} {issues_str}")
        
        # Summary
        print("\n" + "=" * 80)
        print("ANALYSIS SUMMARY")
        print("=" * 80)
        print(f"Total samples analyzed: {total_samples - 1}")
        print(f"Good samples: {len(good_samples)} ({len(good_samples)/(total_samples-1)*100:.1f}%)")
        print(f"Problematic samples: {len(problematic_samples)} ({len(problematic_samples)/(total_samples-1)*100:.1f}%)")
        print()
        
        # Detailed problematic sample analysis
        if very_short_samples:
            print(f"VERY SHORT SAMPLES ({len(very_short_samples)}):")
            print("These are likely the 'tapping' sounds you're hearing:")
            for sample_id, name, duration in very_short_samples[:20]:  # Show first 20
                print(f"  Sample {sample_id}: {name} ({duration:.1f}ms)")
            if len(very_short_samples) > 20:
                print(f"  ... and {len(very_short_samples) - 20} more")
            print()
        
        if quiet_samples:
            print(f"VERY QUIET SAMPLES ({len(quiet_samples)}):")
            for sample_id, name in quiet_samples[:10]:  # Show first 10
                print(f"  Sample {sample_id}: {name}")
            if len(quiet_samples) > 10:
                print(f"  ... and {len(quiet_samples) - 10} more")
            print()
        
        # Issue frequency analysis
        issue_counts = {}
        for _, _, issues in problematic_samples:
            for issue in issues:
                issue_type = issue.split('_')[0]
                issue_counts[issue_type] = issue_counts.get(issue_type, 0) + 1
        
        if issue_counts:
            print("ISSUE FREQUENCY:")
            for issue_type, count in sorted(issue_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"  {issue_type}: {count} samples")
            print()
        
        # Recommendations
        print("RECOMMENDATIONS:")
        if very_short_samples:
            print("1. Filter out samples shorter than 50ms - these cause 'tapping' sounds")
        if quiet_samples:
            print("2. Boost or filter out very quiet samples")
        if len(problematic_samples) > len(good_samples):
            print("3. Consider using a different soundfont - this one has many quality issues")
        else:
            print("3. Most samples are good quality")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during analysis: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """Main analysis function."""
    # Try to find a soundfont
    test_paths = [
        "arachno.sf2",
        "Arachno.sf2", 
        "arachno-soundfont.sf2",
        "Arachno SoundFont - Version 1.0.sf2"
    ]
    
    sf2_path = None
    for path in test_paths:
        if os.path.exists(path):
            sf2_path = path
            break
    
    if not sf2_path:
        print("Soundfont not found. Please provide a path:")
        sf2_path = input("Enter SF2 file path: ").strip()
        if not sf2_path or not os.path.exists(sf2_path):
            print("Invalid or missing soundfont file.")
            return False
    
    return analyze_all_samples(sf2_path)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
