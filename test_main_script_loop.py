#!/usr/bin/env python3
"""
Test the main script's loop function with a short sample to see if it works.
"""

import json
import numpy as np
import sys
import os

# Add the current directory to path
sys.path.append(os.path.dirname(__file__))

from hexsf2newparser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_main_script_loop():
    """Test the main script's loop function."""
    
    # Load settings
    with open('hexsyn_settings.json', 'r') as f:
        settings = json.load(f)
    sf2_path = settings.get('sf2_path', '')
    
    # Initialize parser
    parser = SF2NewParser(sf2_path)
    parser.parse()
    
    # Find a very short sample with loops
    test_sample_id = None
    for i, header in enumerate(parser.sample_headers):
        sample_duration_ms = (header.end - header.start) / header.sample_rate * 1000
        if sample_duration_ms < 5:  # Very short
            loop_info = parser.get_sample_loop_info(i)
            if loop_info and loop_info['has_loop']:
                test_sample_id = i
                break
    
    if test_sample_id is None:
        print("No suitable test sample found")
        return
    
    # Get sample data and loop info
    sample_data = parser.get_raw_sample_data(test_sample_id)
    loop_info = parser.get_sample_loop_info(test_sample_id)
    header = parser.sample_headers[test_sample_id]
    sample_rate = header.sample_rate
    
    print(f"Testing sample: {header.name}")
    print(f"Original length: {len(sample_data)} samples ({len(sample_data)/sample_rate*1000:.1f}ms)")
    print(f"Sample shape: {sample_data.shape}")
    print(f"Loop info: {loop_info}")
    
    # Import the function from the main script
    # We need to extract it - let's just copy the relevant parts
    
    def apply_pitch_bend_with_looping_main(original_sample: np.ndarray, input_offset: int,
                                         output_samples: int, semitone_offset: float,
                                         original_sample_rate: int = 44100, target_sample_rate: int = 44100,
                                         loop_info: dict = None, is_releasing: bool = False):
        """Main script version of the looping function"""
        
        print(f"  Called with: input_offset={input_offset}, output_samples={output_samples}, semitone_offset={semitone_offset}")
        print(f"  Sample shape: {original_sample.shape}")
        print(f"  Loop info: {loop_info}")
        
        # If no loop info, return original sample processing
        if not loop_info or not loop_info['has_loop'] or is_releasing:
            print("  → No loops or releasing, using original sample")
            # Just return a simple segment for testing
            if len(original_sample) >= output_samples:
                return original_sample[:output_samples], input_offset + output_samples
            else:
                # Pad if needed
                if len(original_sample.shape) == 1:
                    padded = np.zeros(output_samples, dtype=original_sample.dtype)
                else:
                    padded = np.zeros((output_samples, original_sample.shape[1]), dtype=original_sample.dtype)
                padded[:len(original_sample)] = original_sample
                return padded, input_offset + len(original_sample)
        
        # Handle SF2 looping
        loop_start = loop_info['loop_start']
        loop_end = loop_info['loop_end']
        loop_length = loop_end - loop_start
        sample_length = len(original_sample)
        
        print(f"  → Loop: {loop_start}-{loop_end} (length: {loop_length})")
        
        # Validate loop points
        if loop_length <= 0 or loop_start >= sample_length or loop_end > sample_length:
            print("  → Invalid loop points, using original sample")
            return original_sample[:output_samples], input_offset + output_samples
        
        # Calculate pitch and rate factors
        pitch_factor = 2 ** (semitone_offset / 12.0)
        rate_factor = original_sample_rate / target_sample_rate
        combined_factor = pitch_factor * rate_factor
        
        print(f"  → Pitch factor: {pitch_factor:.3f}, Rate factor: {rate_factor:.3f}, Combined: {combined_factor:.3f}")
        
        # Calculate how many input samples we need
        input_samples_needed = int(output_samples * combined_factor)
        print(f"  → Need {input_samples_needed} input samples to generate {output_samples} output samples")
        
        # Generate input samples with proper looping
        # Handle both mono and stereo samples
        if len(original_sample.shape) == 1:
            input_segment = np.zeros(input_samples_needed, dtype=original_sample.dtype)
        else:
            input_segment = np.zeros((input_samples_needed, original_sample.shape[1]), dtype=original_sample.dtype)
        
        current_input_pos = input_offset
        samples_generated = 0
        
        print(f"  → Starting generation from input position {current_input_pos}")
        
        loop_count = 0
        while samples_generated < input_samples_needed:
            if current_input_pos < loop_start:
                # Attack phase
                attack_samples_available = loop_start - current_input_pos
                attack_samples_needed = min(attack_samples_available, input_samples_needed - samples_generated)
                
                if attack_samples_needed > 0:
                    print(f"    Attack phase: copying {attack_samples_needed} samples from {current_input_pos}")
                    attack_segment = original_sample[current_input_pos:current_input_pos + attack_samples_needed]
                    input_segment[samples_generated:samples_generated + attack_samples_needed] = attack_segment
                    samples_generated += attack_samples_needed
                    current_input_pos += attack_samples_needed
                    
            elif current_input_pos >= loop_start and current_input_pos < loop_end:
                # Sustain phase: loop the loop section
                remaining_samples = input_samples_needed - samples_generated
                
                # Calculate position within the loop
                loop_pos = (current_input_pos - loop_start) % loop_length
                loop_samples_available = loop_length - loop_pos
                loop_samples_to_copy = min(loop_samples_available, remaining_samples)
                
                if loop_samples_to_copy > 0:
                    if loop_count < 5:  # Only print first few loops
                        print(f"    Sustain phase: copying {loop_samples_to_copy} samples from loop position {loop_pos}")
                    loop_segment = original_sample[loop_start + loop_pos:loop_start + loop_pos + loop_samples_to_copy]
                    input_segment[samples_generated:samples_generated + loop_samples_to_copy] = loop_segment
                    samples_generated += loop_samples_to_copy
                    current_input_pos += loop_samples_to_copy
                    
                    # If we've reached the end of the loop, wrap back to loop start
                    if current_input_pos >= loop_end:
                        if loop_count < 5:  # Only print first few loops
                            print(f"    Wrapping from {current_input_pos} back to loop start {loop_start}")
                        current_input_pos = loop_start
                        loop_count += 1
                        
            else:
                # Beyond loop end
                print(f"    Beyond loop end at position {current_input_pos}")
                break
        
        print(f"  → Generated {samples_generated} input samples, final position: {current_input_pos}, loops: {loop_count}")
        
        # For testing, just return the input segment without resampling
        if len(input_segment) >= output_samples:
            return input_segment[:output_samples], current_input_pos
        else:
            # Pad if needed
            if len(original_sample.shape) == 1:
                padded = np.zeros(output_samples, dtype=original_sample.dtype)
            else:
                padded = np.zeros((output_samples, original_sample.shape[1]), dtype=original_sample.dtype)
            padded[:len(input_segment)] = input_segment
            return padded, current_input_pos
    
    # Test the function
    print(f"\nTesting main script loop extension:")
    
    # Test: Generate 100ms of audio (should trigger looping)
    target_duration_ms = 100
    output_samples = int(target_duration_ms * 0.001 * sample_rate)
    
    try:
        result, new_offset = apply_pitch_bend_with_looping_main(
            sample_data, 0, output_samples, 0.0, sample_rate, sample_rate, loop_info, False
        )
        
        print(f"\nResult:")
        print(f"  Input: {len(sample_data)} samples ({len(sample_data)/sample_rate*1000:.1f}ms)")
        print(f"  Output: {len(result)} samples ({len(result)/sample_rate*1000:.1f}ms)")
        print(f"  New offset: {new_offset}")
        
        # Check if the result is actually extended
        if len(result) > len(sample_data):
            print(f"  ✓ Sample was extended from {len(sample_data)} to {len(result)} samples")
        else:
            print(f"  ✗ Sample was NOT extended (still {len(result)} samples)")
            
        print(f"  ✓ Main script loop function works without crashing!")
        
    except Exception as e:
        print(f"  ✗ Error in main script loop function: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_main_script_loop()
