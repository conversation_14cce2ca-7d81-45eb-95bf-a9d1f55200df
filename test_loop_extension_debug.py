#!/usr/bin/env python3
"""
Debug script to test loop extension functionality in detail.
"""

import json
import sys
from hexsf2newparser import SF2N<PERSON><PERSON><PERSON><PERSON>

def test_loop_extension_debug():
    """Debug test for loop extension functionality."""
    
    # Load settings to get the SF2 file path
    try:
        with open('hexsyn_settings.json', 'r') as f:
            settings = json.load(f)
        sf2_path = settings.get('sf2_path', '')
        if not sf2_path:
            print("Error: No SF2 file path found in settings")
            return False
    except Exception as e:
        print(f"Error loading settings: {e}")
        return False
    
    print(f"Testing loop extension with SF2 file: {sf2_path}")
    
    # Initialize parser
    parser = SF2NewParser(sf2_path)
    
    try:
        # Parse the SF2 file
        parser.parse()
        print(f"Successfully parsed SF2 file with {len(parser.sample_headers)} samples")
        
        # Look for short samples with loops
        short_samples_with_loops = []
        short_samples_without_loops = []
        extended_samples = []
        
        print("\nAnalyzing first 500 samples for loop extension...")

        for sample_id in range(min(500, len(parser.sample_headers))):
            header = parser.sample_headers[sample_id]
            original_sample_length = header.end - header.start
            original_duration_ms = (original_sample_length / header.sample_rate) * 1000 if header.sample_rate > 0 else 0
            
            # Check if it has loop points
            loop_info = parser.get_sample_loop_info(sample_id)
            has_loop = loop_info['has_loop'] if loop_info else False
            
            if original_duration_ms < 100 and original_duration_ms > 0:  # Short samples
                if has_loop:
                    short_samples_with_loops.append((sample_id, header.name, original_duration_ms, loop_info))
                    print(f"  Short sample with loop: {sample_id} ('{header.name}') - {original_duration_ms:.1f}ms, loop: {loop_info['loop_start']}-{loop_info['loop_end']}")
                else:
                    short_samples_without_loops.append((sample_id, header.name, original_duration_ms))
                    if len(short_samples_without_loops) <= 5:  # Show first 5
                        print(f"  Short sample without loop: {sample_id} ('{header.name}') - {original_duration_ms:.1f}ms")
        
        print(f"\nFound {len(short_samples_with_loops)} short samples with loops")
        print(f"Found {len(short_samples_without_loops)} short samples without loops")
        
        # Test loading some short samples with loops
        if short_samples_with_loops:
            print(f"\nTesting loop extension on first 5 short looped samples...")
            for i, (sample_id, name, original_duration_ms, loop_info) in enumerate(short_samples_with_loops[:5]):
                print(f"\nTesting sample {sample_id} ('{name}'):")
                print(f"  Original duration: {original_duration_ms:.1f}ms")
                print(f"  Loop info: {loop_info}")
                
                # Load the sample
                sample_data = parser.get_sample(sample_id)
                if sample_data is not None:
                    actual_duration_ms = (len(sample_data) / parser.sample_headers[sample_id].sample_rate) * 1000
                    print(f"  Loaded sample length: {len(sample_data)} samples ({actual_duration_ms:.1f}ms)")
                    
                    if len(sample_data) > parser.sample_headers[sample_id].end - parser.sample_headers[sample_id].start:
                        extended_samples.append((sample_id, name, original_duration_ms, actual_duration_ms))
                        print(f"  ✓ Sample was extended from {original_duration_ms:.1f}ms to {actual_duration_ms:.1f}ms")
                    else:
                        print(f"  ✗ Sample was NOT extended")
                else:
                    print(f"  ✗ Failed to load sample")
        
        print(f"\nExtension Results:")
        print(f"  Short samples with loops found: {len(short_samples_with_loops)}")
        print(f"  Successfully extended: {len(extended_samples)}")
        
        if extended_samples:
            print(f"\nExtended samples:")
            for sample_id, name, orig_ms, new_ms in extended_samples:
                print(f"  Sample {sample_id} ('{name}'): {orig_ms:.1f}ms -> {new_ms:.1f}ms")
        
        return len(extended_samples) > 0
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Parser doesn't have a close method
        pass

if __name__ == "__main__":
    print("SF2 Loop Extension Debug Test")
    print("=" * 50)
    
    success = test_loop_extension_debug()
    
    if success:
        print("\n✓ Loop extension is working!")
        sys.exit(0)
    else:
        print("\n✗ Loop extension is not working correctly")
        sys.exit(1)
