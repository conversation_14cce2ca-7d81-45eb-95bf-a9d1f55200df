#!/usr/bin/env python3
import struct
import numpy as np
from io import BytesIO
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Optional, Any

# --- SF2 Generator Enumeration (Partial List) ---
# See SF2 specification section 8.1.3
class SF2Generator:
    StartAddrsOffset = 0
    EndAddrsOffset = 1
    StartLoopAddrsOffset = 2
    EndLoopAddrsOffset = 3
    StartAddrsCoarseOffset = 4
    ModLfoToPitch = 5
    VibLfoToPitch = 6
    ModEnvToPitch = 7
    InitialFilterFc = 8
    InitialFilterQ = 9
    ModLfoToFilterFc = 10
    ModEnvToFilterFc = 11
    EndAddrsCoarseOffset = 12
    ModLfoToVolume = 13
    # Unused 14
    ChorusEffectsSend = 15
    ReverbEffectsSend = 16
    Pan = 17
    # Unused 18, 19, 20
    DelayModLFO = 21
    FreqModLFO = 22
    DelayVibLFO = 23
    FreqVibLFO = 24
    DelayModEnv = 25
    AttackModEnv = 26
    HoldModEnv = 27
    DecayModEnv = 28
    SustainModEnv = 29
    ReleaseModEnv = 30
    KeynumToModEnvHold = 31
    KeynumToModEnvDecay = 32
    DelayVolEnv = 33
    AttackVolEnv = 34
    HoldVolEnv = 35
    DecayVolEnv = 36
    SustainVolEnv = 37
    ReleaseVolEnv = 38
    KeynumToVolEnvHold = 39
    KeynumToVolEnvDecay = 40
    Instrument = 41
    # Unused 42
    KeyRange = 43
    VelRange = 44
    StartLoopAddrsCoarseOffset = 45
    Keynum = 46
    Velocity = 47
    InitialAttenuation = 48
    # Unused 49
    EndLoopAddrsCoarseOffset = 50
    CoarseTune = 51
    FineTune = 52
    SampleID = 53
    SampleModes = 54
    # Unused 55
    ScaleTuning = 56
    ExclusiveClass = 57
    OverridingRootKey = 58
    # Unused 59
    EndOper = 60

# --- Data Classes for SF2 Structure ---

@dataclass
class SF2Chunk:
    id: bytes
    size: int
    data_offset: int # Offset in the file where data begins

@dataclass
class SF2GeneratorInfo:
    operator: int
    amount: int # Stored as signed short in file

    def get_amount_range(self) -> Optional[Tuple[int, int]]:
        """Returns the (low, high) byte range if applicable."""
        if self.operator in [SF2Generator.KeyRange, SF2Generator.VelRange]:
            return (self.amount & 0xFF, (self.amount >> 8) & 0xFF)
        return None

@dataclass
class SF2ModulatorInfo:
    # See SF2 spec 8.2.3, 8.3.3, 9.5, 9.6
    # Simplified for now - needs full controller/source/destination mapping
    mod_src_oper: int
    mod_dest_oper: int
    mod_amount: int # Signed short
    mod_amt_src_oper: int
    mod_trans_oper: int

@dataclass
class SF2Zone:
    generators: List[SF2GeneratorInfo] = field(default_factory=list)
    modulators: List[SF2ModulatorInfo] = field(default_factory=list)
    # Helper properties derived from generators
    key_range: Optional[Tuple[int, int]] = None
    vel_range: Optional[Tuple[int, int]] = None
    instrument_id: Optional[int] = None # For preset zones
    sample_id: Optional[int] = None # For instrument zones

    def finalize(self):
        """Calculate helper properties after all generators are added."""
        for gen in self.generators:
            if gen.operator == SF2Generator.KeyRange:
                self.key_range = gen.get_amount_range()
            elif gen.operator == SF2Generator.VelRange:
                self.vel_range = gen.get_amount_range()
            elif gen.operator == SF2Generator.Instrument:
                self.instrument_id = gen.amount
            elif gen.operator == SF2Generator.SampleID:
                self.sample_id = gen.amount

@dataclass
class SF2Instrument:
    name: str
    start_zone_index: int
    num_zones: int
    zones: List[SF2Zone] = field(default_factory=list) # Populated later

@dataclass
class SF2Preset:
    name: str
    preset_num: int
    bank_num: int
    start_zone_index: int
    num_zones: int
    library: int # Unused
    genre: int # Unused
    morphology: int # Unused
    zones: List[SF2Zone] = field(default_factory=list) # Populated later

@dataclass
class SF2SampleHeader:
    name: str
    start: int # Index in smpl data (in samples, not bytes)
    end: int
    loop_start: int
    loop_end: int
    sample_rate: int
    original_pitch: int # MIDI key number (0-127)
    pitch_correction: int # Cents (-128 to 127) -> convert to semitones / 100.0
    sample_link: int # Index to linked sample (e.g., right channel)
    sample_type: int # 1:mono, 2:right, 4:left, 8:linked, 0x8000:ROM

    def get_pitch_correction_semitones(self) -> float:
        # Convert signed byte stored as int to actual signed value
        corr = self.pitch_correction
        if corr >= 128:
            corr -= 256
        return corr / 100.0

@dataclass
class SF2FileInfo:
    version: Tuple[int, int] = (0, 0)
    sound_engine: str = ""
    bank_name: str = ""
    rom_name: str = ""
    rom_version: Tuple[int, int] = (0, 0)
    creation_date: str = ""
    author: str = ""
    product: str = ""
    copyright: str = ""
    comments: str = ""
    tools: str = ""

# --- Main Parser Class ---

class SF2NewParser:
    def __init__(self, filepath: str):
        self.filepath = filepath
        self.file = None
        self.chunks: Dict[bytes, SF2Chunk] = {}
        self.info = SF2FileInfo()
        self.sample_headers: List[SF2SampleHeader] = []
        self.presets: List[SF2Preset] = []
        self.instruments: List[SF2Instrument] = []
        # Raw zone/gen/mod data before being assigned to presets/instruments
        self._preset_zones_raw: List[SF2Zone] = []
        self._instrument_zones_raw: List[SF2Zone] = []
        # Sample data (loaded separately if needed)
        self.samples: Optional[np.ndarray] = None # Holds the entire smpl chunk data

    def _read_chunk_header(self) -> Optional[SF2Chunk]:
        """Reads the 8-byte header (ID + size) of the next chunk."""
        try:
            header_data = self.file.read(8)
            if len(header_data) < 8:
                return None # End of file or incomplete chunk
            chunk_id, chunk_size = struct.unpack('<4sI', header_data)
            return SF2Chunk(id=chunk_id, size=chunk_size, data_offset=self.file.tell())
        except Exception as e:
            print(f"Error reading chunk header: {e}")
            return None

    def _parse_riff_header(self):
        """Parses the main RIFF container."""
        print("Parsing RIFF header...")
        chunk = self._read_chunk_header()
        if not chunk or chunk.id != b'RIFF':
            raise ValueError("Invalid SF2: Missing RIFF chunk.")

        # Check for 'sfbk' type
        sfbk_type = self.file.read(4)
        if sfbk_type != b'sfbk':
            raise ValueError("Invalid SF2: RIFF type is not 'sfbk'.")
        print(f"RIFF chunk found (size: {chunk.size}), type: sfbk")

        # Read LIST chunks within RIFF
        list_end = chunk.data_offset + chunk.size - 4 # -4 for 'sfbk' type read
        while self.file.tell() < list_end:
            list_chunk = self._read_chunk_header()
            if not list_chunk:
                break # Should not happen in valid file before list_end

            if list_chunk.id == b'LIST':
                list_type = self.file.read(4)
                print(f"  Found LIST chunk (size: {list_chunk.size}), type: {list_type.decode('ascii')}")
                if list_type == b'INFO':
                    self._parse_info_list(list_chunk)
                elif list_type == b'sdta':
                    self._parse_sdta_list(list_chunk)
                elif list_type == b'pdta':
                    self._parse_pdta_list(list_chunk)
                else:
                    print(f"    Skipping unknown LIST type: {list_type}")
                    self.file.seek(list_chunk.data_offset + list_chunk.size) # Skip data
            else:
                 print(f"  Skipping unexpected chunk within RIFF: {list_chunk.id}")
                 self.file.seek(list_chunk.data_offset + list_chunk.size) # Skip data

            # Ensure alignment (chunks are padded to even size)
            if self.file.tell() % 2 != 0:
                self.file.seek(1, 1) # Seek 1 byte relative to current pos

    def _parse_info_list(self, list_chunk: SF2Chunk):
        """Parses the INFO LIST chunk."""
        print("    Parsing INFO...")
        end_offset = list_chunk.data_offset + list_chunk.size - 4 # -4 for 'INFO' type read
        while self.file.tell() < end_offset:
            sub_chunk = self._read_chunk_header()
            if not sub_chunk: break

            data = self.file.read(sub_chunk.size).rstrip(b'\x00')
            try:
                data_str = data.decode('ascii') # Or latin-1? Spec is vague.
            except UnicodeDecodeError:
                data_str = data.decode('latin-1', errors='ignore')

            # Store info based on chunk ID (e.g., 'ifil', 'isng', 'INAM', etc.)
            if sub_chunk.id == b'ifil':
                if len(data) >= 4:
                    major, minor = struct.unpack('<HH', data[:4])
                    self.info.version = (major, minor)
                    print(f"      ifil (Version): {major}.{minor}")
            elif sub_chunk.id == b'isng':
                self.info.sound_engine = data_str
                print(f"      isng (Sound Engine): {data_str}")
            elif sub_chunk.id == b'INAM':
                self.info.bank_name = data_str
                print(f"      INAM (Bank Name): {data_str}")
            # Add other INFO sub-chunks as needed (ICRD, IENG, IPRD, ICOP, ICMT, ISFT)
            elif sub_chunk.id == b'ICRD': self.info.creation_date = data_str
            elif sub_chunk.id == b'IENG': self.info.author = data_str
            elif sub_chunk.id == b'IPRD': self.info.product = data_str
            elif sub_chunk.id == b'ICOP': self.info.copyright = data_str
            elif sub_chunk.id == b'ICMT': self.info.comments = data_str
            elif sub_chunk.id == b'ISFT': self.info.tools = data_str
            else:
                print(f"      Skipping unknown INFO sub-chunk: {sub_chunk.id}")

            if self.file.tell() % 2 != 0: self.file.seek(1, 1)

    def _parse_sdta_list(self, list_chunk: SF2Chunk):
        """Parses the sdta LIST chunk (contains sample data)."""
        print("    Parsing sdta...")
        end_offset = list_chunk.data_offset + list_chunk.size - 4 # -4 for 'sdta' type read
        while self.file.tell() < end_offset:
            sub_chunk = self._read_chunk_header()
            if not sub_chunk: break

            if sub_chunk.id == b'smpl':
                print(f"      Found smpl chunk (size: {sub_chunk.size}) at offset {sub_chunk.data_offset}")
                # Store chunk info, but don't read data yet
                self.chunks[b'smpl'] = sub_chunk
                self.file.seek(sub_chunk.data_offset + sub_chunk.size) # Skip data for now
            else:
                print(f"      Skipping unknown sdta sub-chunk: {sub_chunk.id}")
                self.file.seek(sub_chunk.data_offset + sub_chunk.size)

            if self.file.tell() % 2 != 0: self.file.seek(1, 1)

    def _parse_pdta_list(self, list_chunk: SF2Chunk):
        """Parses the pdta LIST chunk (presets, instruments, zones, etc.)."""
        print("    Parsing pdta...")
        end_offset = list_chunk.data_offset + list_chunk.size - 4 # -4 for 'pdta' type read
        while self.file.tell() < end_offset:
            sub_chunk = self._read_chunk_header()
            if not sub_chunk: break

            print(f"      Found pdta sub-chunk: {sub_chunk.id.decode('ascii')} (size: {sub_chunk.size})")
            # Store chunk info, read data later in specific order
            self.chunks[sub_chunk.id] = sub_chunk
            self.file.seek(sub_chunk.data_offset + sub_chunk.size) # Skip data for now

            if self.file.tell() % 2 != 0: self.file.seek(1, 1)

    def _read_pdta_records(self, chunk_id: bytes, record_size: int, format_str: str) -> List[Tuple]:
        """Reads records from a pdta sub-chunk."""
        if chunk_id not in self.chunks:
            print(f"Warning: Required pdta chunk '{chunk_id.decode('ascii')}' not found.")
            return []

        chunk = self.chunks[chunk_id]
        self.file.seek(chunk.data_offset)
        num_records = chunk.size // record_size
        records = []
        print(f"      Reading {num_records} records from {chunk_id.decode('ascii')} (record size: {record_size})")

        if num_records <= 1: # Last record is a terminator
             print(f"      Chunk {chunk_id.decode('ascii')} has only terminator record or is empty.")
             return []

        for i in range(num_records):
            record_data = self.file.read(record_size)
            if len(record_data) < record_size:
                print(f"Warning: Incomplete record {i} in {chunk_id.decode('ascii')}")
                break
            try:
                unpacked_data = struct.unpack(format_str, record_data)
                records.append(unpacked_data)
            except struct.error as e:
                print(f"Error unpacking record {i} in {chunk_id.decode('ascii')}: {e}")
                # Attempt to recover or skip? For now, stop reading this chunk.
                break

        # The last record in each pdta chunk is a terminator and should be ignored
        return records[:-1]

    def _load_pdta_data(self):
        """Loads data from all pdta sub-chunks in the correct order."""
        print("    Loading PDTA sub-chunk data...")

        # 1. Preset Headers (phdr) - Defines presets
        # Format: 20s H H H I I I (name, preset, bank, bagIndex, library, genre, morphology)
        phdr_records = self._read_pdta_records(b'phdr', 38, '<20sHHHIII')
        for name_bytes, preset, bank, bag_idx, lib, genre, morph in phdr_records:
            name = name_bytes.decode('ascii', errors='ignore').strip('\x00')
            self.presets.append(SF2Preset(name, preset, bank, bag_idx, 0, lib, genre, morph)) # num_zones calculated later
        print(f"      Loaded {len(self.presets)} presets.")

        # 2. Preset Zones (pbag) - Links presets to generators/modulators
        # Format: H H (genIndex, modIndex)
        pbag_records = self._read_pdta_records(b'pbag', 4, '<HH')
        # Store temporarily, used to link zones later
        self._pbag_raw = pbag_records
        print(f"      Loaded {len(self._pbag_raw)} preset zone index pairs.")

        # 3. Preset Modulators (pmod) - Defines modulators for preset zones
        # Format: H H h H H (modSrcOper, modDestOper, modAmount, modAmtSrcOper, modTransOper)
        # See SF2 Spec 8.2.3, 9.5 for details on structure
        pmod_records = self._read_pdta_records(b'pmod', 10, '<HHhHH')
        self._pmod_raw = [SF2ModulatorInfo(*rec) for rec in pmod_records]
        print(f"      Loaded {len(self._pmod_raw)} preset modulators.")

        # 4. Preset Generators (pgen) - Defines generators for preset zones
        # Format: H h (genOper, genAmount)
        pgen_records = self._read_pdta_records(b'pgen', 4, '<Hh')
        self._pgen_raw = [SF2GeneratorInfo(*rec) for rec in pgen_records]
        print(f"      Loaded {len(self._pgen_raw)} preset generators.")

        # 5. Instrument Headers (inst) - Defines instruments
        # Format: 20s H (name, bagIndex)
        inst_records = self._read_pdta_records(b'inst', 22, '<20sH')
        for name_bytes, bag_idx in inst_records:
            name = name_bytes.decode('ascii', errors='ignore').strip('\x00')
            self.instruments.append(SF2Instrument(name, bag_idx, 0)) # num_zones calculated later
        print(f"      Loaded {len(self.instruments)} instruments.")

        # 6. Instrument Zones (ibag) - Links instruments to generators/modulators
        # Format: H H (genIndex, modIndex)
        ibag_records = self._read_pdta_records(b'ibag', 4, '<HH')
        self._ibag_raw = ibag_records
        print(f"      Loaded {len(self._ibag_raw)} instrument zone index pairs.")

        # 7. Instrument Modulators (imod) - Defines modulators for instrument zones
        # Format: H H h H H (modSrcOper, modDestOper, modAmount, modAmtSrcOper, modTransOper)
        imod_records = self._read_pdta_records(b'imod', 10, '<HHhHH')
        self._imod_raw = [SF2ModulatorInfo(*rec) for rec in imod_records]
        print(f"      Loaded {len(self._imod_raw)} instrument modulators.")

        # 8. Instrument Generators (igen) - Defines generators for instrument zones
        # Format: H h (genOper, genAmount)
        igen_records = self._read_pdta_records(b'igen', 4, '<Hh')
        self._igen_raw = [SF2GeneratorInfo(*rec) for rec in igen_records]
        print(f"      Loaded {len(self._igen_raw)} instrument generators.")

        # 9. Sample Headers (shdr) - Defines sample metadata
        # Format: 20s I I I I I B b H H (name, start, end, loopStart, loopEnd, sampleRate, originalPitch, pitchCorrection, sampleLink, sampleType)
        shdr_records = self._read_pdta_records(b'shdr', 46, '<20sIIIIIBbHH')
        for name_bytes, start, end, loop_start, loop_end, sr, pitch, corr, link, type_ in shdr_records:
            name = name_bytes.decode('ascii', errors='ignore').strip('\x00')
            self.sample_headers.append(SF2SampleHeader(name, start, end, loop_start, loop_end, sr, pitch, corr, link, type_))
        print(f"      Loaded {len(self.sample_headers)} sample headers.")

        # --- Assemble Zones ---
        print("    Assembling zones...")
        self._preset_zones_raw = self._assemble_zones(self._pbag_raw, self._pgen_raw, self._pmod_raw)
        self._instrument_zones_raw = self._assemble_zones(self._ibag_raw, self._igen_raw, self._imod_raw)

        # --- Link Zones to Presets and Instruments ---
        print("    Linking zones to presets and instruments...")
        self._link_zones_to_items(self.presets, self._preset_zones_raw)
        self._link_zones_to_items(self.instruments, self._instrument_zones_raw)

        # Cleanup raw data
        del self._pbag_raw, self._pgen_raw, self._pmod_raw
        del self._ibag_raw, self._igen_raw, self._imod_raw

    def _assemble_zones(self, bag_records, gen_records, mod_records) -> List[SF2Zone]:
        """Creates Zone objects from raw bag, gen, and mod records."""
        zones = []
        num_bags = len(bag_records)
        for i in range(num_bags):
            gen_start_idx, mod_start_idx = bag_records[i]

            # Determine end index for generators and modulators for this zone
            gen_end_idx = bag_records[i+1][0] if (i + 1) < num_bags else len(gen_records)
            mod_end_idx = bag_records[i+1][1] if (i + 1) < num_bags else len(mod_records)

            zone = SF2Zone(
                generators=gen_records[gen_start_idx:gen_end_idx],
                modulators=mod_records[mod_start_idx:mod_end_idx]
            )
            zone.finalize() # Calculate key/vel range etc.
            zones.append(zone)
        print(f"      Assembled {len(zones)} zones.")
        return zones

    def _link_zones_to_items(self, items: List[Any], zones: List[SF2Zone]):
        """Assigns the correct zones to each Preset or Instrument."""
        num_items = len(items)
        for i in range(num_items):
            item = items[i]
            start_zone_idx = item.start_zone_index
            end_zone_idx = items[i+1].start_zone_index if (i + 1) < num_items else len(zones)
            item.num_zones = end_zone_idx - start_zone_idx
            item.zones = zones[start_zone_idx:end_zone_idx]
            # print(f"      Linked {item.num_zones} zones to {type(item).__name__} '{item.name}' (indices {start_zone_idx}-{end_zone_idx-1})")

    def load_sample_data(self):
        """Loads the actual sample data from the 'smpl' chunk."""
        if self.samples is not None:
            print("Sample data already loaded.")
            return

        if b'smpl' not in self.chunks:
            print("Warning: 'smpl' chunk not found. Cannot load sample data.")
            self.samples = np.array([], dtype=np.int16)
            return

        smpl_chunk = self.chunks[b'smpl']
        print(f"Loading sample data from 'smpl' chunk (size: {smpl_chunk.size} bytes)...")
        
        try:
            self.file = open(self.filepath, 'rb')
            self.file.seek(smpl_chunk.data_offset)
            
            # Determine if we need to read in chunks (for very large files)
            if smpl_chunk.size > 50 * 1024 * 1024:  # If larger than 50MB
                print("Large sample data detected, reading in chunks...")
                chunks = []
                chunk_size = 10 * 1024 * 1024  # 10MB chunks
                bytes_read = 0
                
                while bytes_read < smpl_chunk.size:
                    read_size = min(chunk_size, smpl_chunk.size - bytes_read)
                    chunks.append(self.file.read(read_size))
                    bytes_read += read_size
                    print(f"Read {bytes_read/1024/1024:.1f}MB of {smpl_chunk.size/1024/1024:.1f}MB")
                
                sample_data_bytes = b''.join(chunks)
            else:
                sample_data_bytes = self.file.read(smpl_chunk.size)
            
            # Close the file as we're done reading the sample data
            self.file.close()
            self.file = None
            
            # Convert bytes to numpy array
            self.samples = np.frombuffer(sample_data_bytes, dtype=np.int16)
            print(f"Loaded {self.samples.size} sample points ({self.samples.size * 2} bytes).")
            
        except Exception as e:
            print(f"Error loading sample data: {str(e)}")
            self.samples = np.array([], dtype=np.int16)
            if self.file:
                self.file.close()
                self.file = None

    def get_sample(self, sample_id: int) -> Optional[np.ndarray]:
        """
        Extracts and returns a specific sample as float32 numpy array.
        Handles mono/stereo and normalizes to [-1.0, 1.0].
        
        Args:
            sample_id: Index of the sample to extract
            
        Returns:
            Normalized float32 numpy array with sample data, or None if sample can't be loaded
        """
        if self.samples is None:
            print(f"Loading sample data for sample {sample_id}")
            self.load_sample_data()
        
        if self.samples is None or len(self.samples) == 0:
            print(f"Error: No sample data available for sample {sample_id}")
            return None
        
        if sample_id >= len(self.sample_headers):
            print(f"Error: Invalid sample ID {sample_id}, max is {len(self.sample_headers)-1}")
            return None

        header = self.sample_headers[sample_id]
        
        # Debug info
        sample_type_str = self._get_sample_type_string(header.sample_type)
        # print(f"Sample {sample_id} '{header.name}': start={header.start}, end={header.end}, "
        #       f"loop=[{header.loop_start}:{header.loop_end}], type={header.sample_type} ({sample_type_str}), "
        #       f"rate={header.sample_rate}, pitch={header.original_pitch}, link={header.sample_link}")
        
        # Validate sample range
        if header.start >= self.samples.size or header.end > self.samples.size:
            print(f"Warning: Sample {sample_id} ('{header.name}') data range [{header.start}:{header.end}] "
                  f"exceeds loaded sample buffer size {self.samples.size}. Adjusting to fit.")
            # Adjust the end to fit within the buffer, but don't truncate too aggressively
            original_end = header.end
            header.end = min(header.end, self.samples.size)
            if header.start >= header.end:
                print(f"Warning: Sample {sample_id} has invalid range after adjustment. Skipping.")
                return None
            print(f"  Adjusted sample {sample_id} end from {original_end} to {header.end}")

        # Additional validation: check for reasonable sample length
        sample_length = header.end - header.start

        # Check for very short samples that might need loop extension
        duration_ms = (sample_length / header.sample_rate) * 1000 if header.sample_rate > 0 else 0

        if sample_length < 10:
            print(f"Warning: Sample {sample_id} ('{header.name}') is extremely short ({sample_length} samples)")
            # Don't skip, but warn - some percussion samples might be legitimately short
        
        if header.start >= header.end:
            print(f"Warning: Sample {sample_id} ('{header.name}') has invalid range (start >= end). Skipping.")
            return None

        # Extract raw int16 data
        try:
            raw_data = self.samples[header.start:header.end]

            # Ensure we have a reasonable minimum sample length
            if len(raw_data) < 100:  # Less than 100 samples is very short
                print(f"Warning: Sample {sample_id} ('{header.name}') is very short ({len(raw_data)} samples). "
                      f"This may cause audio artifacts.")

            # Convert to float32 and normalize
            float_data = raw_data.astype(np.float32) / 32768.0

            # Additional quality check for very quiet samples
            max_amplitude = np.max(np.abs(float_data))
            if max_amplitude < 0.001:  # Very quiet samples
                print(f"Filtering out very quiet sample {sample_id} ('{header.name}'): max amplitude {max_amplitude:.6f}")
                return None
            
            # Store loop information for dynamic playback - don't pre-extend samples
            # The audio renderer will handle looping based on note duration
            if (header.sample_type & 1) and header.loop_start < header.loop_end:
                # Validate loop points are within range
                loop_start = max(0, min(header.loop_start - header.start, len(float_data) - 1))
                loop_end = max(loop_start + 1, min(header.loop_end - header.start, len(float_data)))

                if loop_end > loop_start + 1:  # Valid loop found
                    # Just store the loop info - the audio renderer will handle dynamic looping
                    pass
            
            # Handle stereo samples
            # If this is part of a stereo pair, we'll return it as is and let the caller
            # handle combining with its pair if needed
            if header.sample_type & 0x2:  # Right channel
                # Flag it for stereo handling but return as is
                pass
            elif header.sample_type & 0x4:  # Left channel
                # Flag it for stereo handling but return as is
                pass
            elif header.sample_type & 0x8:  # Linked sample
                # This sample should be processed alongside its linked sample
                # For now, just return it as is
                pass
            
            # Apply pitch correction if needed (advanced implementation would do this during playback)
            pitch_correction = header.get_pitch_correction_semitones()
            if abs(pitch_correction) > 0.01:
                # Just note this - actual pitch shifting would happen during playback
                pass
            
            return float_data
            
        except Exception as e:
            print(f"Error extracting sample {sample_id} ('{header.name}'): {str(e)}")
            return None

    def parse(self):
        """Opens the file and parses the SF2 structure."""
        print(f"Opening SF2 file: {self.filepath}")
        try:
            with open(self.filepath, 'rb') as f:
                self.file = f
                self._parse_riff_header()

                # After finding all pdta chunks, load their data
                if self.chunks: # Check if any chunks were found
                    self._load_pdta_data()
                else:
                     print("Warning: No data chunks found in the file.")

                print("SF2 parsing complete.")
                # Load sample data while the file is still open
                self.load_sample_data()

        except FileNotFoundError:
            print(f"Error: File not found at {self.filepath}")
            raise
        except ValueError as e:
            print(f"Error parsing SF2 file: {e}")
            raise
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            raise
        finally:
            self.file = None # Ensure file handle is released

    def find_sample_for_note(self, preset_index: int, note: int, velocity: int = 64) -> Optional[Tuple[int, Dict]]:
        """
        Find the most appropriate sample for a given MIDI note in a preset.
        
        Args:
            preset_index: Index of the preset to use
            note: MIDI note number (0-127)
            velocity: MIDI velocity (0-127)
            
        Returns:
            Tuple of (sample_id, {dict of generators}) or None if no suitable sample found
        """
        try:
            if preset_index >= len(self.presets):
                print(f"Error: Invalid preset index {preset_index}")
                return None
                
            preset = self.presets[preset_index]
            
            # Step 1: Find matching preset zone
            matching_preset_zone = None
            preset_generators = {}
            global_preset_generators = {}
            
            # First check for global zone in preset (zone without instrument reference)
            for zone in preset.zones:
                if zone.instrument_id is None:
                    global_preset_generators = {gen.operator: gen.amount for gen in zone.generators}
                    break
            
            # Then find specific matching zone
            for zone in preset.zones:
                # Skip global zone (already processed)
                if zone.instrument_id is None:
                    continue
                    
                # Check if this zone's key and velocity ranges include our note
                if zone.key_range and (note < zone.key_range[0] or note > zone.key_range[1]):
                    continue
                    
                if zone.vel_range and (velocity < zone.vel_range[0] or velocity > zone.vel_range[1]):
                    continue
                    
                # Found a matching zone
                matching_preset_zone = zone
                preset_generators = {gen.operator: gen.amount for gen in zone.generators}
                break
            
            if not matching_preset_zone:
                return None
                
            # Step 2: Get the instrument from the matching preset zone
            instrument_id = matching_preset_zone.instrument_id
            if instrument_id is None or instrument_id >= len(self.instruments):
                print(f"Error: Invalid instrument ID {instrument_id} in preset {preset.name}")
                return None
                
            instrument = self.instruments[instrument_id]
            
            # Step 3: Find matching instrument zone
            instrument_zone = None
            instrument_generators = {}
            global_instrument_generators = {}
            
            # First check for global zone in instrument (zone without sample reference)
            for zone in instrument.zones:
                if zone.sample_id is None:
                    global_instrument_generators = {gen.operator: gen.amount for gen in zone.generators}
                    break
            
            # Then find specific matching zone
            for zone in instrument.zones:
                # Skip global zone (already processed)
                if zone.sample_id is None:
                    continue
                    
                # Check if this zone's key and velocity ranges include our note
                if zone.key_range and (note < zone.key_range[0] or note > zone.key_range[1]):
                    continue
                    
                if zone.vel_range and (velocity < zone.vel_range[0] or velocity > zone.vel_range[1]):
                    continue
                    
                # Found a matching zone
                instrument_zone = zone
                instrument_generators = {gen.operator: gen.amount for gen in zone.generators}
                break
                
            if not instrument_zone:
                return None
                
            # Step 4: Get the sample ID from the matching instrument zone
            sample_id = instrument_zone.sample_id
            if sample_id is None or sample_id >= len(self.sample_headers):
                print(f"Error: Invalid sample ID {sample_id} in instrument {instrument.name}")
                return None
            
            # Step 5: Combine generators in the correct order (global preset → local preset → global instrument → local instrument)
            all_generators = {}
            all_generators.update(global_preset_generators)
            all_generators.update(preset_generators)
            all_generators.update(global_instrument_generators)
            all_generators.update(instrument_generators)
                
            return (sample_id, all_generators)
        
        except Exception as e:
            print(f"Error finding sample for note {note}: {str(e)}")
            return None
    
    def get_raw_sample_data(self, sample_id: int) -> Optional[np.ndarray]:
        """
        Get raw sample data without any stereo processing.
        This is used internally to avoid recursive calls.
        Includes the same processing as get_sample() but without stereo handling.
        """
        if self.samples is None:
            self.load_sample_data()

        if self.samples is None or len(self.samples) == 0:
            return None

        if sample_id >= len(self.sample_headers):
            return None

        header = self.sample_headers[sample_id]

        # Validate sample range (same as original get_sample)
        if header.start >= self.samples.size or header.end > self.samples.size:
            # Adjust the end to fit within the buffer, but don't truncate too aggressively
            original_end = header.end
            header.end = min(header.end, self.samples.size)
            if header.start >= header.end:
                return None

        # Additional validation: check for reasonable sample length
        sample_length = header.end - header.start

        # Check for very short samples that might need loop extension
        duration_ms = (sample_length / header.sample_rate) * 1000 if header.sample_rate > 0 else 0

        if sample_length < 10:
            # Don't skip, but warn - some percussion samples might be legitimately short
            pass

        if header.start >= header.end:
            return None

        # Extract raw int16 data (same as original get_sample)
        try:
            raw_data = self.samples[header.start:header.end]

            # Ensure we have a reasonable minimum sample length
            if len(raw_data) < 100:  # Less than 100 samples is very short
                pass  # Don't print warning here to avoid spam

            # Convert to float32 and normalize
            float_data = raw_data.astype(np.float32) / 32768.0

            # Additional quality check for very quiet samples
            max_amplitude = np.max(np.abs(float_data))
            if max_amplitude < 0.001:  # Very quiet samples
                return None

            # Store loop information for dynamic playback - don't pre-extend samples
            # The audio renderer will handle looping based on note duration
            if (header.sample_type & 1) and header.loop_start < header.loop_end:
                # Validate loop points are within range
                loop_start = max(0, min(header.loop_start - header.start, len(float_data) - 1))
                loop_end = max(loop_start + 1, min(header.loop_end - header.start, len(float_data)))

                if loop_end > loop_start + 1:  # Valid loop found
                    # Just store the loop info - the audio renderer will handle dynamic looping
                    pass

            return float_data

        except Exception as e:
            print(f"Error extracting raw sample {sample_id}: {e}")
            return None

    def get_stereo_sample(self, sample_id: int) -> Optional[np.ndarray]:
        """
        Get a proper stereo sample by finding and combining the left/right channels
        if the sample is part of a stereo pair.

        Args:
            sample_id: The ID of the sample to get

        Returns:
            A stereo sample with left and right channels properly arranged,
            or the original sample if not part of a stereo pair.
        """
        try:
            if sample_id >= len(self.sample_headers):
                return None

            header = self.sample_headers[sample_id]

            # SF2 Sample Type flags:
            # 1 = monoSample
            # 2 = rightSample
            # 4 = leftSample
            # 8 = linkedSample
            # 0x8000 = romSample

            # For mono samples (type 1), just duplicate to stereo
            if header.sample_type == 1:
                mono_data = self.get_raw_sample_data(sample_id)
                if mono_data is None:
                    return None
                return np.column_stack((mono_data, mono_data))

            # For left channel samples (type 4), combine with right channel
            elif header.sample_type == 4:
                left_data = self.get_raw_sample_data(sample_id)
                if left_data is None:
                    return None

                # Get the right channel
                right_id = header.sample_link
                if right_id >= len(self.sample_headers) or right_id < 0:
                    print(f"Warning: Left sample {sample_id} has invalid right link {right_id}")
                    return np.column_stack((left_data, left_data))

                right_header = self.sample_headers[right_id]

                # Smart stereo detection: check if this is a real stereo pair or broken linking
                if right_header.sample_type == 2:  # Proper right channel
                    # Check if the right channel links back to this left channel
                    if right_header.sample_link == sample_id:
                        # This looks like a proper stereo pair
                        right_data = self.get_raw_sample_data(right_id)
                        if right_data is not None:
                            # Additional validation: check if samples are similar length and different content
                            length_ratio = len(right_data) / len(left_data) if len(left_data) > 0 else 0
                            if 0.8 <= length_ratio <= 1.2:  # Similar lengths
                                # Check if they're actually different (not just duplicated mono)
                                min_length = min(len(left_data), len(right_data))
                                if min_length > 100:  # Enough samples to compare
                                    left_sample = left_data[:min_length]
                                    right_sample = right_data[:min_length]
                                    correlation = np.corrcoef(left_sample, right_sample)[0, 1]

                                    if not np.isnan(correlation) and correlation < 0.95:  # Different enough to be stereo
                                        # This is a real stereo pair
                                        return np.column_stack((left_sample, right_sample))
                                    else:
                                        # Too similar - probably duplicated mono
                                        print(f"Info: Left sample {sample_id} and right {right_id} are too similar (corr={correlation:.3f}) - treating as mono")
                                        return np.column_stack((left_data, left_data))
                                else:
                                    # Too short to analyze, just combine
                                    min_length = min(len(left_data), len(right_data))
                                    return np.column_stack((left_data[:min_length], right_data[:min_length]))
                            else:
                                print(f"Info: Left sample {sample_id} and right {right_id} have very different lengths - treating as mono")
                                return np.column_stack((left_data, left_data))
                        else:
                            print(f"Warning: Could not load right channel {right_id}")
                            return np.column_stack((left_data, left_data))
                    else:
                        print(f"Info: Left sample {sample_id} links to right {right_id}, but right doesn't link back - treating as mono")
                        return np.column_stack((left_data, left_data))

                elif right_header.sample_type == 1:  # Linked to mono sample
                    # This is broken stereo linking (common in Arachno)
                    print(f"Info: Left sample {sample_id} links to mono sample {right_id} - treating as mono")
                    return np.column_stack((left_data, left_data))

                else:  # Some other unexpected type
                    print(f"Warning: Left sample {sample_id} links to unexpected sample type {right_id} (type {right_header.sample_type})")
                    return np.column_stack((left_data, left_data))

            # For right channel samples (type 2), find the left channel and process from there
            elif header.sample_type == 2:
                left_id = header.sample_link
                if left_id >= len(self.sample_headers) or left_id < 0:
                    print(f"Warning: Right sample {sample_id} has invalid left link {left_id}")
                    right_data = self.get_raw_sample_data(sample_id)
                    if right_data is None:
                        return None
                    return np.column_stack((right_data, right_data))

                # Process from the left channel to avoid confusion
                return self.get_stereo_sample(left_id)

            # For any other type, treat as mono
            else:
                mono_data = self.get_raw_sample_data(sample_id)
                if mono_data is None:
                    return None
                return np.column_stack((mono_data, mono_data))

        except Exception as e:
            print(f"Error creating stereo sample for sample {sample_id}: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return None
    
    def modify_sample_for_playback(self, sample_data: np.ndarray, original_header, generators: Dict[int, int], note: int) -> np.ndarray:
        """
        Apply generator modifications to a sample for accurate playback.

        Args:
            sample_data: The raw sample data to modify
            original_header: The sample header for the original sample
            generators: Dictionary of generator types to values
            note: MIDI note number being played

        Returns:
            Modified sample data
        """
        try:
            if sample_data is None:
             return None

            # Ensure we have a 2D array (stereo)
            if len(sample_data.shape) == 1:
                sample_data = np.column_stack((sample_data, sample_data))

            # Apply various generator modifications

            # 1. Root key override (default to the original pitch if not specified)
            root_key = original_header.original_pitch
            if SF2Generator.OverridingRootKey in generators:
                root_key = generators[SF2Generator.OverridingRootKey]

            # 2. Fine tuning (in cents, -99 to +99)
            fine_tune = 0
            if hasattr(original_header, 'pitch_correction'):
                fine_tune = original_header.pitch_correction
                # Make sure the value is interpreted correctly as signed
                if fine_tune > 127:
                    fine_tune -= 256

            if SF2Generator.FineTune in generators:
                gen_fine_tune = generators[SF2Generator.FineTune]
                # Make sure the value is interpreted correctly as signed
                if gen_fine_tune > 32767:
                    gen_fine_tune -= 65536
                fine_tune += gen_fine_tune

            # 3. Coarse tuning (in semitones, -120 to +120)
            coarse_tune = 0
            if SF2Generator.CoarseTune in generators:
                coarse_tune = generators[SF2Generator.CoarseTune]
                # Make sure the value is interpreted correctly as signed
                if coarse_tune > 32767:
                    coarse_tune -= 65536

            # 4. Scale tuning (in cents per semitone, typically 100)
            scale_tuning = 100  # Default: 100 cents per semitone
            if SF2Generator.ScaleTuning in generators:
                scale_tuning = generators[SF2Generator.ScaleTuning]
                # Ensure it's not zero to avoid division issues
                if scale_tuning == 0:
                    scale_tuning = 1

            # Calculate pitch offset in semitones
            # The formula is:
            # semitones = (note - root_key) * (scale_tuning / 100.0) + coarse_tune + (fine_tune / 100.0)
            pitch_offset_semitones = 0.0

            # Calculate the key difference part
            key_diff = note - root_key
            key_diff_scaled = key_diff * (scale_tuning / 100.0)

            # Add all the components together
            pitch_offset_semitones = key_diff_scaled + coarse_tune + (fine_tune / 100.0)

            # Debug log to check values
            # print(f"Note: {note}, Root: {root_key}, Key diff: {key_diff}")
            # print(f"Scale: {scale_tuning}, Coarse: {coarse_tune}, Fine: {fine_tune}")
            # print(f"Total pitch offset: {pitch_offset_semitones} semitones")

            # Pitch shift the sample if needed
            if abs(pitch_offset_semitones) > 0.01:
                stretch_factor = 2 ** (pitch_offset_semitones / 12.0)

                original_length = len(sample_data)
                new_length = int(original_length / stretch_factor)

                # Ensure reasonable limits
                if new_length < 10:
                    return sample_data  # Too small to process

                # Debug log
                # print(f"Stretching sample by factor {stretch_factor} (new length: {new_length})")

                indices = np.linspace(0, original_length - 1, new_length)

                # Create output array with correct shape (ensure it's 2D for stereo)
                shifted_sample = np.zeros((new_length, 2), dtype=sample_data.dtype)

                # Process each channel
                for ch in range(min(2, sample_data.shape[1])):
                    shifted_sample[:, ch] = np.interp(indices,
                                                np.arange(original_length),
                                                sample_data[:, ch])

                return shifted_sample

            return sample_data

        except Exception as e:
            print(f"Error modifying sample for playback: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return sample_data

    def get_preset_list(self) -> List[Dict[str, any]]:
        """
        Get a list of all available presets with their details.

        Returns:
            List of dictionaries containing preset information
        """
        preset_list = []
        for i, preset in enumerate(self.presets):
            preset_info = {
                'index': i,
                'name': preset.name,
                'bank': preset.bank_num,
                'program': preset.preset_num,
                'zones': preset.num_zones
            }
            preset_list.append(preset_info)
        return preset_list

    def find_preset_by_program(self, bank: int = 0, program: int = 0) -> Optional[int]:
        """
        Find a preset by bank and program number.

        Args:
            bank: Bank number (default 0 for General MIDI)
            program: Program number (0-127)

        Returns:
            Preset index if found, None otherwise
        """
        for i, preset in enumerate(self.presets):
            if preset.bank_num == bank and preset.preset_num == program:
                return i
        return None

    def get_samples_for_preset(self, preset_index: int, progress_callback=None) -> Dict[int, Tuple[int, Dict]]:
        """
        Get sample mappings for a specific preset across all MIDI notes.

        Args:
            preset_index: Index of the preset to use
            progress_callback: Optional callback for progress reporting

        Returns:
            Dictionary mapping MIDI note numbers to (sample_id, generators) tuples
        """
        if preset_index >= len(self.presets):
            print(f"Error: Invalid preset index {preset_index}")
            return {}

        preset = self.presets[preset_index]
        sample_map = {}
        successful_notes = 0

        if progress_callback:
            progress_callback(f"Mapping samples for preset '{preset.name}'", 0, 128)

        for note in range(128):
            if progress_callback and note % 16 == 0:
                progress_callback(f"Processing note {note}", note, 128)

            try:
                result = self.find_sample_for_note(preset_index, note, 64)
                if result:
                    sample_id, generators = result
                    sample_map[note] = (sample_id, generators)
                    successful_notes += 1
            except Exception as e:
                if progress_callback and note % 16 == 0:
                    progress_callback(f"Warning: Failed to map note {note}: {str(e)}", note, 128)
                continue

        if progress_callback:
            progress_callback(f"Successfully mapped {successful_notes} notes for preset '{preset.name}'", 128, 128)

        return sample_map

    def get_general_midi_mapping(self) -> Dict[int, int]:
        """
        Create a mapping from General MIDI program numbers to preset indices.

        Returns:
            Dictionary mapping GM program numbers (0-127) to preset indices
        """
        gm_mapping = {}

        # General MIDI program names for reference
        gm_programs = [
            # Piano (0-7)
            "Acoustic Grand Piano", "Bright Acoustic Piano", "Electric Grand Piano", "Honky-tonk Piano",
            "Electric Piano 1", "Electric Piano 2", "Harpsichord", "Clavi",
            # Chromatic Percussion (8-15)
            "Celesta", "Glockenspiel", "Music Box", "Vibraphone", "Marimba", "Xylophone", "Tubular Bells", "Dulcimer",
            # Organ (16-23)
            "Drawbar Organ", "Percussive Organ", "Rock Organ", "Church Organ", "Reed Organ", "Accordion", "Harmonica", "Tango Accordion",
            # Guitar (24-31)
            "Acoustic Guitar (nylon)", "Acoustic Guitar (steel)", "Electric Guitar (jazz)", "Electric Guitar (clean)",
            "Electric Guitar (muted)", "Overdriven Guitar", "Distortion Guitar", "Guitar harmonics",
            # Bass (32-39)
            "Acoustic Bass", "Electric Bass (finger)", "Electric Bass (pick)", "Fretless Bass",
            "Slap Bass 1", "Slap Bass 2", "Synth Bass 1", "Synth Bass 2",
            # Strings (40-47)
            "Violin", "Viola", "Cello", "Contrabass", "Tremolo Strings", "Pizzicato Strings", "Orchestral Harp", "Timpani",
            # Ensemble (48-55)
            "String Ensemble 1", "String Ensemble 2", "SynthStrings 1", "SynthStrings 2",
            "Choir Aahs", "Voice Oohs", "Synth Voice", "Orchestra Hit",
            # Brass (56-63)
            "Trumpet", "Trombone", "Tuba", "Muted Trumpet", "French Horn", "Brass Section", "SynthBrass 1", "SynthBrass 2",
            # Reed (64-71)
            "Soprano Sax", "Alto Sax", "Tenor Sax", "Baritone Sax", "Oboe", "English Horn", "Bassoon", "Clarinet",
            # Pipe (72-79)
            "Piccolo", "Flute", "Recorder", "Pan Flute", "Blown Bottle", "Shakuhachi", "Whistle", "Ocarina",
            # Synth Lead (80-87)
            "Lead 1 (square)", "Lead 2 (sawtooth)", "Lead 3 (calliope)", "Lead 4 (chiff)",
            "Lead 5 (charang)", "Lead 6 (voice)", "Lead 7 (fifths)", "Lead 8 (bass + lead)",
            # Synth Pad (88-95)
            "Pad 1 (new age)", "Pad 2 (warm)", "Pad 3 (polysynth)", "Pad 4 (choir)",
            "Pad 5 (bowed)", "Pad 6 (metallic)", "Pad 7 (halo)", "Pad 8 (sweep)",
            # Synth Effects (96-103)
            "FX 1 (rain)", "FX 2 (soundtrack)", "FX 3 (crystal)", "FX 4 (atmosphere)",
            "FX 5 (brightness)", "FX 6 (goblins)", "FX 7 (echoes)", "FX 8 (sci-fi)",
            # Ethnic (104-111)
            "Sitar", "Banjo", "Shamisen", "Koto", "Kalimba", "Bag pipe", "Fiddle", "Shanai",
            # Percussive (112-119)
            "Tinkle Bell", "Agogo", "Steel Drums", "Woodblock", "Taiko Drum", "Melodic Tom", "Synth Drum", "Reverse Cymbal",
            # Sound Effects (120-127)
            "Guitar Fret Noise", "Breath Noise", "Seashore", "Bird Tweet", "Telephone Ring", "Helicopter", "Applause", "Gunshot"
        ]

        # Try to find presets that match GM program names or numbers
        for program_num in range(128):
            preset_index = self.find_preset_by_program(0, program_num)  # Bank 0 for GM
            if preset_index is not None:
                gm_mapping[program_num] = preset_index
            else:
                # Try to find by name matching
                gm_name = gm_programs[program_num].lower()
                for i, preset in enumerate(self.presets):
                    preset_name = preset.name.lower()
                    # Simple name matching - look for key words
                    if any(word in preset_name for word in gm_name.split() if len(word) > 3):
                        gm_mapping[program_num] = i
                        break

        return gm_mapping

    def find_drum_preset(self) -> Optional[int]:
        """
        Find the best drum kit preset in the soundfont.

        Returns:
            Preset index of the drum kit, or None if not found
        """
        # First, try to find a preset in bank 128 (standard GM drum bank)
        for i, preset in enumerate(self.presets):
            if preset.bank_num == 128:
                return i

        # If no bank 128 preset, look for drum-related names
        drum_keywords = ['drum', 'kit', 'percussion', 'perc', 'drums', 'standard kit', 'gm drums']
        for i, preset in enumerate(self.presets):
            preset_name = preset.name.lower()
            if any(keyword in preset_name for keyword in drum_keywords):
                return i

        # Last resort: look for any preset with "standard" in the name (often drum kits)
        for i, preset in enumerate(self.presets):
            preset_name = preset.name.lower()
            if 'standard' in preset_name:
                return i

        return None

    def get_gm_drum_mapping(self) -> Dict[int, str]:
        """
        Get General MIDI drum note mapping for channel 10.

        Returns:
            Dictionary mapping MIDI note numbers to drum sound names
        """
        # Standard General MIDI drum mapping (notes 35-81)
        gm_drums = {
            35: "Acoustic Bass Drum",
            36: "Bass Drum 1",
            37: "Side Stick",
            38: "Acoustic Snare",
            39: "Hand Clap",
            40: "Electric Snare",
            41: "Low Floor Tom",
            42: "Closed Hi Hat",
            43: "High Floor Tom",
            44: "Pedal Hi-Hat",
            45: "Low Tom",
            46: "Open Hi-Hat",
            47: "Low-Mid Tom",
            48: "Hi-Mid Tom",
            49: "Crash Cymbal 1",
            50: "High Tom",
            51: "Ride Cymbal 1",
            52: "Chinese Cymbal",
            53: "Ride Bell",
            54: "Tambourine",
            55: "Splash Cymbal",
            56: "Cowbell",
            57: "Crash Cymbal 2",
            58: "Vibraslap",
            59: "Ride Cymbal 2",
            60: "Hi Bongo",
            61: "Low Bongo",
            62: "Mute Hi Conga",
            63: "Open Hi Conga",
            64: "Low Conga",
            65: "High Timbale",
            66: "Low Timbale",
            67: "High Agogo",
            68: "Low Agogo",
            69: "Cabasa",
            70: "Maracas",
            71: "Short Whistle",
            72: "Long Whistle",
            73: "Short Guiro",
            74: "Long Guiro",
            75: "Claves",
            76: "Hi Wood Block",
            77: "Low Wood Block",
            78: "Mute Cuica",
            79: "Open Cuica",
            80: "Mute Triangle",
            81: "Open Triangle"
        }
        return gm_drums

    def get_sample_loop_info(self, sample_id: int) -> Optional[Dict]:
        """Get loop information for a sample."""
        if sample_id < 0 or sample_id >= len(self.sample_headers):
            return None

        header = self.sample_headers[sample_id]

        # Check if sample has valid loop points
        if (header.sample_type & 1) and header.loop_start < header.loop_end:
            # Convert absolute loop points to relative positions within the sample
            sample_start = header.start
            loop_start_relative = max(0, header.loop_start - sample_start)
            loop_end_relative = max(loop_start_relative + 1, header.loop_end - sample_start)

            return {
                'has_loop': True,
                'loop_start': loop_start_relative,
                'loop_end': loop_end_relative,
                'loop_length': loop_end_relative - loop_start_relative,
                'sample_length': header.end - header.start
            }

        return {
            'has_loop': False,
            'loop_start': 0,
            'loop_end': 0,
            'loop_length': 0,
            'sample_length': header.end - header.start
        }

    def get_looped_sample_for_duration(self, sample_id: int, duration_seconds: float) -> np.ndarray:
        """
        Generate a sample with proper loop-based sustain for the specified duration.

        This implements proper SF2 loop behavior:
        1. Play attack section (start to loop_start)
        2. Repeat loop section for the sustain duration
        3. Play release section (loop_end to end) if any

        Args:
            sample_id: The sample ID to load
            duration_seconds: How long the note should sustain

        Returns:
            np.ndarray: The looped sample data, or None if failed
        """
        try:
            # Get the original sample data
            original_sample = self.get_sample(sample_id)
            if original_sample is None:
                return None

            header = self.sample_headers[sample_id]
            loop_info = self.get_sample_loop_info(sample_id)

            # If no loop, just return the original sample (it will be cut/extended by the renderer)
            if not loop_info['has_loop']:
                return original_sample

            # Calculate sample positions
            loop_start = loop_info['loop_start']
            loop_end = loop_info['loop_end']
            sample_length = len(original_sample)

            # Validate loop points
            if loop_start >= sample_length or loop_end > sample_length or loop_start >= loop_end:
                return original_sample

            # Calculate required length for the duration
            required_samples = int(duration_seconds * header.sample_rate)

            # If the original sample is already long enough, return it as-is
            if len(original_sample) >= required_samples:
                return original_sample

            # Split the sample into sections
            attack_section = original_sample[:loop_start] if loop_start > 0 else np.array([])
            loop_section = original_sample[loop_start:loop_end]
            release_section = original_sample[loop_end:] if loop_end < sample_length else np.array([])

            # Calculate how much sustain we need
            attack_length = len(attack_section)
            release_length = len(release_section)
            loop_length = len(loop_section)

            if loop_length == 0:
                return original_sample

            # Calculate sustain duration (total - attack - release)
            sustain_samples = max(0, required_samples - attack_length - release_length)

            # Calculate how many loop repetitions we need
            if sustain_samples > 0:
                loop_repetitions = max(1, int(np.ceil(sustain_samples / loop_length)))

                # Create the extended loop section
                extended_loop = np.tile(loop_section, loop_repetitions)

                # Trim to exact length needed
                if len(extended_loop) > sustain_samples:
                    extended_loop = extended_loop[:sustain_samples]

                # Combine all sections
                result_parts = []
                if len(attack_section) > 0:
                    result_parts.append(attack_section)
                if len(extended_loop) > 0:
                    result_parts.append(extended_loop)
                if len(release_section) > 0:
                    result_parts.append(release_section)

                if result_parts:
                    return np.concatenate(result_parts)
                else:
                    return original_sample
            else:
                # No sustain needed, just return attack + release
                if len(attack_section) > 0 and len(release_section) > 0:
                    return np.concatenate([attack_section, release_section])
                elif len(attack_section) > 0:
                    return attack_section
                elif len(release_section) > 0:
                    return release_section
                else:
                    return original_sample

        except Exception as e:
            print(f"Error generating looped sample for sample {sample_id}: {e}")
            return self.get_sample(sample_id)  # Fallback to original sample

    def _get_sample_type_string(self, sample_type: int) -> str:
        """Convert sample type flags to readable string."""
        types = []
        if sample_type & 1:
            types.append("mono")
        if sample_type & 2:
            types.append("right")
        if sample_type & 4:
            types.append("left")
        if sample_type & 8:
            types.append("linked")
        if sample_type & 0x8000:
            types.append("ROM")
        return "|".join(types) if types else f"unknown({sample_type})"

    def create_channel_preset_mapping(self, channel_programs: Dict[int, int] = None) -> Dict[int, int]:
        """
        Create a mapping from MIDI channels to preset indices.

        Args:
            channel_programs: Optional dictionary mapping channels to GM program numbers
                            If None, uses default GM mapping (channel 9 = drums, others = piano)

        Returns:
            Dictionary mapping MIDI channels (0-15) to preset indices
        """
        channel_mapping = {}
        gm_mapping = self.get_general_midi_mapping()

        if channel_programs is None:
            # Default mapping: channel 9 (10 in 1-based) is drums, others are piano
            default_program = 0  # Acoustic Grand Piano
            drum_preset = self.find_drum_preset()

            for channel in range(16):
                if channel == 9:  # Channel 10 (1-based) is traditionally drums
                    if drum_preset is not None:
                        channel_mapping[channel] = drum_preset
                        print(f"      Mapped channel 10 (drums) to preset {drum_preset}: '{self.presets[drum_preset].name}'")
                    elif default_program in gm_mapping:
                        channel_mapping[channel] = gm_mapping[default_program]
                    else:
                        channel_mapping[channel] = 0  # First preset as fallback
                else:
                    if default_program in gm_mapping:
                        channel_mapping[channel] = gm_mapping[default_program]
                    else:
                        channel_mapping[channel] = 0  # First preset as fallback
        else:
            # Use provided channel programs
            drum_preset = self.find_drum_preset()

            for channel in range(16):
                program = channel_programs.get(channel, 0)

                if channel == 9 or program == -1:  # Channel 10 or special drum marker
                    if drum_preset is not None:
                        channel_mapping[channel] = drum_preset
                        print(f"      Mapped channel {channel+1} (drums) to preset {drum_preset}: '{self.presets[drum_preset].name}'")
                    else:
                        # Fallback to first preset if no drums found
                        channel_mapping[channel] = 0
                        print(f"      Warning: No drum kit found, using preset 0 for channel {channel+1}")
                else:
                    if program in gm_mapping:
                        channel_mapping[channel] = gm_mapping[program]
                    else:
                        channel_mapping[channel] = 0  # First preset as fallback

        return channel_mapping

# --- Example Usage ---
if __name__ == "__main__":
    # Replace with the actual path to your SF2 file
    # sf2_path = "path/to/your/soundfont.sf2"
    sf2_path = input("Enter path to SF2 file: ")

    try:
        parser = SF2NewParser(sf2_path)
        parser.parse()

        print("\n--- SF2 Info ---")
        print(f"Version: {parser.info.version[0]}.{parser.info.version[1]}")
        print(f"Bank Name: {parser.info.bank_name}")
        print(f"Sound Engine: {parser.info.sound_engine}")
        print(f"Author: {parser.info.author}")
        print(f"Date: {parser.info.creation_date}")
        print(f"Tools: {parser.info.tools}")
        print(f"Comments: {parser.info.comments}")


        print(f"\n--- Found {len(parser.presets)} Presets ---")
        for i, preset in enumerate(parser.presets):
            print(f"  Preset {i}: Bank={preset.bank_num}, Num={preset.preset_num}, Name='{preset.name}', Zones={preset.num_zones}")
            # Optionally print zone details
            # for zi, zone in enumerate(preset.zones):
            #     print(f"    Zone {zi}: Key={zone.key_range}, Vel={zone.vel_range}, InstID={zone.instrument_id}")
            #     for gen in zone.generators:
            #         print(f"      Gen {gen.operator}: {gen.amount}")

        print(f"\n--- Found {len(parser.instruments)} Instruments ---")
        for i, inst in enumerate(parser.instruments):
            print(f"  Instrument {i}: Name='{inst.name}', Zones={inst.num_zones}")
            # Optionally print zone details
            # for zi, zone in enumerate(inst.zones):
            #     print(f"    Zone {zi}: Key={zone.key_range}, Vel={zone.vel_range}, SampleID={zone.sample_id}")
            #     for gen in zone.generators:
            #         print(f"      Gen {gen.operator}: {gen.amount}")


        print(f"\n--- Found {len(parser.sample_headers)} Sample Headers ---")
        for i, sh in enumerate(parser.sample_headers):
             print(f"  Sample {i}: Name='{sh.name}', Rate={sh.sample_rate}, Pitch={sh.original_pitch}, Corr={sh.get_pitch_correction_semitones():.2f} st, Type={sh.sample_type}, Link={sh.sample_link}, Range=[{sh.start}:{sh.end}], Loop=[{sh.loop_start}:{sh.loop_end}]")

        # --- Example: Load and access sample data ---
        # parser.load_sample_data()
        # if parser.samples is not None and len(parser.sample_headers) > 0:
        #     sample_id_to_get = 0 # Example: get the first sample
        #     sample_data = parser.get_sample(sample_id_to_get)
        #     if sample_data is not None:
        #         print(f"\n--- Sample {sample_id_to_get} Data ---")
        #         print(f"  Shape: {sample_data.shape}")
        #         print(f"  Min/Max: {np.min(sample_data):.4f} / {np.max(sample_data):.4f}")
        #         print(f"  Sample Rate (from header): {parser.sample_headers[sample_id_to_get].sample_rate}")


    except (FileNotFoundError, ValueError, Exception) as e:
        print(f"\nAn error occurred: {e}")