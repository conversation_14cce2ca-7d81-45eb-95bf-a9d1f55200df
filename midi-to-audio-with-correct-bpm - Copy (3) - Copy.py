import struct
from typing import List, Tuple, Generator, Dict, Set, Optional
import os
import numpy as np
import soundfile as sf
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from tqdm import tqdm
import multiprocessing
from scipy import signal
import imgui
import glfw
import OpenGL.GL as gl
from imgui.integrations.glfw import GlfwRenderer
import threading
from queue import Queue
import time
from tkinter import filedialog
import tkinter as tk
import sys
import json
import os.path
import random # Added
from numba import njit
from hexsf2newparser import SF2NewParser

# Hide tkinter root window
root = tk.Tk()
root.withdraw()

class MIDIEvent:
    __slots__ = ['time', 'event_type', 'channel', 'param1', 'param2', 'pitch_bend', 'cc_number', 'cc_value']
    def __init__(self, time, event_type, channel=None, param1=None, param2=None, pitch_bend=None, cc_number=None, cc_value=None):
        self.time = time
        self.event_type = event_type
        self.channel = channel
        self.param1 = param1
        self.param2 = param2
        self.pitch_bend = pitch_bend
        self.cc_number = cc_number
        self.cc_value = cc_value

class ProgressCallback:
    def __init__(self, message_queue):
        self.message_queue = message_queue
        self.last_update = 0

    def __call__(self, desc, current, total):
        current_time = time.time()
        if current_time - self.last_update >= 0.1:  # Update at most every 100ms
            percentage = (current / total) * 100 if total > 0 else 0
            self.message_queue.put(f"{desc}: {percentage:.1f}%")
            self.last_update = current_time

class MIDIRendererGUI:
    def __init__(self):
        # Default values
        self.midi_path = ""
        self.sf2_path = ""  # Changed from sample_folder
        self.polyphony_limit = 512
        self.release_time = 0.05
        self.chunk_duration = 65535.0
        self.processing_fps = 0  # Added
        self.fps_fluctuation = 0.0 # Added
        
        # Normalization settings
        self.target_peak = 0.08
        self.target_rms = -1.5
        self.attack = 100
        self.release = 1000
        self.max_boost_db = 0.08
        self.max_cut_db = -48

        # Sound Effects
        self.reverb_amount = 0.0
        self.chorus_amount = 0.0
        
        # Status information
        self.log_messages = []
        self.is_processing = False
        self.current_progress = 0
        self.message_queue = Queue()
        
        # QOL
        self.disable_release_fade = False
        
        # Load saved settings if they exist
        try:
            settings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'hexsyn_settings.json')
            if os.path.exists(settings_path):
                with open(settings_path, 'r') as f:
                    settings = json.load(f)
                    
                self.midi_path = settings.get('midi_path', self.midi_path)
                self.sf2_path = settings.get('sf2_path', self.sf2_path)  # Changed from sample_folder
                self.polyphony_limit = settings.get('polyphony_limit', self.polyphony_limit)
                self.release_time = settings.get('release_time', self.release_time)
                self.chunk_duration = settings.get('chunk_duration', self.chunk_duration)
                self.target_peak = settings.get('target_peak', self.target_peak)
                self.target_rms = settings.get('target_rms', self.target_rms)
                self.attack = settings.get('attack', self.attack)
                self.release = settings.get('release', self.release)
                self.max_boost_db = settings.get('max_boost_db', self.max_boost_db)
                self.max_cut_db = settings.get('max_cut_db', self.max_cut_db)
                self.disable_release_fade = settings.get('disable_release_fade', self.disable_release_fade)
                self.processing_fps = settings.get('processing_fps', self.processing_fps) # Added
                self.fps_fluctuation = settings.get('fps_fluctuation', self.fps_fluctuation) # Added
                self.reverb_amount = settings.get('reverb_amount', self.reverb_amount) # Added
                self.chorus_amount = settings.get('chorus_amount', self.chorus_amount) # Added
        except Exception as e:
            print(f"Error loading settings: {str(e)}")
            
        # Initialize GLFW and ImGui
        self.init_imgui()

    def init_imgui(self):
        if not glfw.init():
            return False

        # Create window with default size
        monitor = glfw.get_primary_monitor()
        mode = glfw.get_video_mode(monitor)
        window_width = int(mode.size.width * 0.5)
        window_height = int(mode.size.height * 0.75)

        self.window = glfw.create_window(window_width, window_height, "HexSyn", None, None)
        if not self.window:
            glfw.terminate()
            return False

        glfw.make_context_current(self.window)
        imgui.create_context()
        self.impl = GlfwRenderer(self.window)
        
        # Set window position to center of screen
        window_pos_x = int((mode.size.width - window_width) / 2)
        window_pos_y = int((mode.size.height - window_height) / 2)
        glfw.set_window_pos(self.window, window_pos_x, window_pos_y)
        
        return True
        
    def auto_save_settings(self):
        settings = {
            'midi_path': self.midi_path,
            'sf2_path': self.sf2_path,  # Changed from sample_folder
            'polyphony_limit': self.polyphony_limit,
            'release_time': self.release_time,
            'chunk_duration': self.chunk_duration,
            'target_peak': self.target_peak,
            'target_rms': self.target_rms,
            'attack': self.attack,
            'release': self.release,
            'max_boost_db': self.max_boost_db,
            'max_cut_db': self.max_cut_db,
            'disable_release_fade': self.disable_release_fade,
            'processing_fps': self.processing_fps, # Added
            'fps_fluctuation': self.fps_fluctuation, # Added
            'reverb_amount': self.reverb_amount, # Added
            'chorus_amount': self.chorus_amount # Added
        }
        
        try:
            settings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'hexsyn_settings.json')
            with open(settings_path, 'w') as f:
                json.dump(settings, f, indent=4)
        except Exception as e:
            pass  # Silently fail for auto-save to avoid spam

    def log(self, message):
        self.message_queue.put(message)

    def update_log(self):
        while not self.message_queue.empty():
            self.log_messages.append(self.message_queue.get())
            if len(self.log_messages) > 1000:  # Limit log size
                self.log_messages.pop(0)

    def select_midi_file(self):
        filename = filedialog.askopenfilename(
            title="Select MIDI File",
            filetypes=[("MIDI files", "*.mid"), ("All files", "*.*")]
        )
        if filename:
            self.midi_path = filename
            self.log(f"Selected MIDI file: {filename}")
            self.auto_save_settings()
    
    def select_sf2_file(self):  # Changed from select_sample_folder
        filename = filedialog.askopenfilename(
            title="Select SoundFont File",
            filetypes=[("SoundFont files", "*.sf2"), ("All files", "*.*")]
        )
        if filename:
            self.sf2_path = filename
            self.log(f"Selected SoundFont file: {filename}")
            self.auto_save_settings()

    def render_gui(self):
        # Set window size to 80% of the screen
        viewport = imgui.get_main_viewport()
        window_width = viewport.size.x * 1
        window_height = viewport.size.y * 1
        window_pos_x = (viewport.size.x - window_width) / 2
        window_pos_y = (viewport.size.y - window_height) / 2
    
        imgui.set_next_window_position(window_pos_x, window_pos_y)
        imgui.set_next_window_size(window_width, window_height)
    
        imgui.begin("HexSyn", True, 
                imgui.WINDOW_NO_RESIZE | 
                imgui.WINDOW_NO_MOVE)
    
        # File selection
        imgui.text("MIDI File:")
        imgui.same_line()
        if imgui.button("Open File##midi"):
            self.select_midi_file()
        imgui.text(self.midi_path)
    
        imgui.text("SoundFont File:")  # Changed from Sample Folder
        imgui.same_line()
        if imgui.button("Open SF2##soundfont"):  # Changed button label
            self.select_sf2_file()
        imgui.text(self.sf2_path)
    
        imgui.separator()
    
        # Settings
        changed, self.polyphony_limit = imgui.input_int("Polyphony Limit (per Track)", self.polyphony_limit)
        if changed:
            self.auto_save_settings()
    
        changed, self.release_time = imgui.input_float("Release Time (s)", self.release_time, format="%.3f")
        if changed:
            self.auto_save_settings()
    
        changed, self.chunk_duration = imgui.input_float("Chunk Duration (s)", self.chunk_duration, format="%.1f")
        if changed:
            self.auto_save_settings()
            
        changed, self.processing_fps = imgui.input_int("Processing FPS (0=disabled)", self.processing_fps) # Added
        if changed:
            self.auto_save_settings()
            
        changed, self.fps_fluctuation = imgui.input_float("FPS Fluctuation (0=disabled)", self.fps_fluctuation, format="%.3f") # Added
        if changed:
            self.auto_save_settings()
    
        imgui.separator()
        imgui.text("Audio Normalization Settings:")
        
        changed, self.target_peak = imgui.input_float("Target Peak (dB)", self.target_peak, format="%.3f")
        if changed:
            self.auto_save_settings()
    
        changed, self.target_rms = imgui.input_float("Target RMS (dB)", self.target_rms, format="%.3f")
        if changed:
            self.auto_save_settings()
    
        changed, self.attack = imgui.input_float("Attack", self.attack, format="%.1f")
        if changed:
            self.auto_save_settings()
    
        changed, self.release = imgui.input_float("Release", self.release, format="%.1f")
        if changed:
            self.auto_save_settings()
    
        changed, self.max_boost_db = imgui.input_float("Max Boost (dB)", self.max_boost_db, format="%.3f")
        if changed:
            self.auto_save_settings()
    
        changed, self.max_cut_db = imgui.input_float("Max Cut (dB)", self.max_cut_db, format="%.3f")
        if changed:
            self.auto_save_settings()
    
        imgui.separator()
        imgui.text("Sample Handling Settings:")
        
        changed, self.disable_release_fade = imgui.checkbox(
            "Disable fading out samples (Can cause clicks when killing the samples, can also improve rendering speed)", 
            self.disable_release_fade
        )
        if changed:
            self.auto_save_settings()

        imgui.separator()
        imgui.text("Sound Effects:")

        changed, self.reverb_amount = imgui.slider_float("Reverb (%)", self.reverb_amount, 0.0, 100.0, "%.1f")
        if changed:
            self.auto_save_settings()

        changed, self.chorus_amount = imgui.slider_float("Chorus (%)", self.chorus_amount, 0.0, 100.0, "%.1f")
        if changed:
            self.auto_save_settings()
    
        imgui.separator()
    
        # Process button
        if not self.is_processing:
            if imgui.button("Process MIDI", width=120, height=30):
                if not self.midi_path or not self.sf2_path:  # Changed to sf2_path
                    self.log("Please select both MIDI file and sample folder")
                else:
                    self.start_processing()
        else:
            imgui.text("Processing... Please wait")
    
        # Log window
        imgui.begin_child("Log Window", 0, 0, border=True)
        imgui.push_text_wrap_pos(0)
        for message in self.log_messages:
            imgui.text(message)
        if self.log_messages:
            imgui.set_scroll_here_y(1.0)  # Auto-scroll to bottom
        imgui.pop_text_wrap_pos()
        imgui.end_child()
    
        imgui.end()
        
        self.update_log()
        # Continuing from the previous code...
        
    def start_processing(self):
        if not os.path.exists(self.midi_path) or not os.path.exists(self.sf2_path):  # Changed condition
            self.log("Please select valid MIDI and SoundFont files")
            return
    
        self.is_processing = True
        self.log_messages.clear()
        
        def process_thread():
            try:
                self.log(f"Loading SoundFont file: {self.sf2_path}")
                
                # Create a progress callback that routes to our log
                def sf_progress_callback(desc, current, total):
                    if isinstance(desc, str) and desc.startswith("Warning:"):
                        # Don't flood the UI with every warning
                        if current % 10 == 0:  # Only show every 10th warning
                            self.log(desc)
                    elif isinstance(current, int) and isinstance(total, int) and total > 0:
                        percentage = (current / total) * 100
                        if current % (max(1, total // 20)) == 0 or current == total:  # ~20 updates
                            self.log(f"{desc}: {percentage:.1f}%")
                    else:
                        self.log(desc)
                
                # Analyze MIDI file to determine program changes for each channel
                self.log("Analyzing MIDI file for instrument programs...")
                channel_programs = analyze_midi_programs(self.midi_path)

                # Log the detected programs
                program_info = []
                for channel, program in channel_programs.items():
                    if channel == 9 or program == -1:  # Drum channel
                        program_info.append(f"Ch{channel+1}: Drums")
                    else:
                        program_info.append(f"Ch{channel+1}: GM{program}")

                self.log(f"Detected programs: {', '.join(program_info[:8])}{'...' if len(program_info) > 8 else ''}")

                # Analyze MIDI file to determine which notes are actually used
                self.log("Analyzing MIDI file for required notes to optimize memory usage...")
                required_notes = analyze_midi_notes_used(self.midi_path)
                self.log(f"Found {len(required_notes)} unique (channel, note) combinations in MIDI file")

                # Show some statistics about the required notes
                if required_notes:
                    channels_used = set(channel for channel, note in required_notes)
                    notes_used = set(note for channel, note in required_notes)
                    min_note = min(notes_used) if notes_used else 0
                    max_note = max(notes_used) if notes_used else 0
                    self.log(f"MIDI uses {len(channels_used)} channels, note range: {min_note} to {max_note}")

                # Use the improved load_samples function that handles proper instrument mapping
                # and only loads the samples that are actually needed
                samples = load_samples(
                    self.sf2_path,
                    progress_callback=sf_progress_callback,
                    disable_release_fade=self.disable_release_fade,
                    release_time=self.release,
                    target_sample_rate=44100,  # Use standard sample rate for pre-processing
                    channel_programs=channel_programs,  # Pass the detected programs
                    required_notes=required_notes  # Only load samples that are actually used
                )
                
                if not samples:
                    self.log("No usable samples found in the SoundFont. Please try a different file.")
                    return

                # Calculate memory savings
                total_possible_samples = 16 * 128  # 16 channels * 128 notes
                memory_savings_percent = ((total_possible_samples - len(samples)) / total_possible_samples) * 100
                self.log(f"Successfully loaded {len(samples)} samples (saved {memory_savings_percent:.1f}% memory vs loading all {total_possible_samples} possible samples)")

                # Show the note range and channel information available
                if samples:
                    channels_with_samples = set(channel for channel, note in samples.keys())
                    unique_notes = set(note for channel, note in samples.keys())
                    min_note = min(unique_notes) if unique_notes else 0
                    max_note = max(unique_notes) if unique_notes else 0
                    self.log(f"Loaded samples for {len(channels_with_samples)} channels, note range: {min_note} to {max_note}")
                
                output_path = os.path.splitext(self.midi_path)[0] + "_rendered.mp3"
                self.log(f"Beginning MIDI rendering to {output_path}")
                
                render_midi_to_audio(
                    self.midi_path,
                    samples,
                    output_path,
                    self.chunk_duration,
                    self.polyphony_limit,
                    self.release_time,
                    self.target_peak,
                    self.target_rms,
                    self.attack,
                    self.release,
                    self.max_boost_db,
                    self.max_cut_db,
                    self.message_queue,
                    self.disable_release_fade,
                    self.processing_fps, # Added
                    self.fps_fluctuation, # Added
                    self.reverb_amount, # Added
                    self.chorus_amount # Added
                )
                
            except Exception as e:
                self.log(f"Error: {str(e)}")
                import traceback
                self.log(traceback.format_exc())
            finally:
                self.is_processing = False
        
        threading.Thread(target=process_thread, daemon=True).start()

    def run(self):
        while not glfw.window_should_close(self.window):
            glfw.poll_events()
            self.impl.process_inputs()
            
            # Clear frame
            gl.glClearColor(0.1, 0.1, 0.1, 1)
            gl.glClear(gl.GL_COLOR_BUFFER_BIT)
            
            # Start new frame
            imgui.new_frame()
            self.render_gui()
            
            # End frame
            imgui.end_frame()
            
            # Render
            imgui.render()
            self.impl.render(imgui.get_draw_data())
            
            # Swap buffers
            glfw.swap_buffers(self.window)
        
        self.impl.shutdown()
        glfw.terminate()

def parse_variable_length(data: bytes, offset: int) -> Tuple[int, int]:
    value = 0
    while True:
        byte = data[offset]
        value = (value << 7) | (byte & 0x7F)
        offset += 1
        if not (byte & 0x80):
            break
    return value, offset

def parse_midi_events(data: bytes) -> Generator[MIDIEvent, None, None]:
    offset = 0
    time = 0
    running_status = None

    # RPN state tracking and pitch bend range (from midi_loader.py)
    rpn_state = {}
    pitch_bend_range = {}
    channel_pitch_bend = {}  # Track current pitch bend per channel (in semitones)

    for ch in range(16):
        rpn_state[ch] = {'RPN_MSB': None, 'RPN_LSB': None, 'fine': 0}
        pitch_bend_range[ch] = 2.0  # Default pitch bend range is 2 semitones
        channel_pitch_bend[ch] = 0.0  # Current pitch bend in semitones
    
    while offset < len(data):
        delta_time, offset = parse_variable_length(data, offset)
        time += delta_time
        
        if data[offset] < 0x80:  # Running status
            if running_status is None:
                raise ValueError("Running status without previous status byte")
            status = running_status
        else:
            status = data[offset]
            offset += 1
            running_status = status

        if status == 0xFF:  # Meta event
            meta_type = data[offset]
            offset += 1
            length, offset = parse_variable_length(data, offset)
            if meta_type == 0x51:  # Set tempo
                tempo = struct.unpack('>I', b'\x00' + data[offset:offset+3])[0]
                yield MIDIEvent(time, 'set_tempo', param1=tempo)
            elif meta_type == 0x2F:  # End of track
                yield MIDIEvent(time, 'end_of_track')
                break
            offset += length
        elif status == 0xF0 or status == 0xF7:  # SysEx event
            length, offset = parse_variable_length(data, offset)
            offset += length
        else:
            channel = status & 0x0F
            event_type = status & 0xF0
            
            if event_type in [0x80, 0x90, 0xA0, 0xB0, 0xE0]:
                if event_type == 0xB0:  # Control Change (CC events)
                    param1, param2 = data[offset], data[offset+1]
                    offset += 2
                    cc_number, cc_value = param1, param2

                    # Handle RPN (Registered Parameter Number) events for pitch bend range
                    if cc_number == 101:  # RPN MSB
                        rpn_state[channel]['RPN_MSB'] = cc_value
                    elif cc_number == 100:  # RPN LSB
                        rpn_state[channel]['RPN_LSB'] = cc_value
                    elif cc_number == 6:  # Data Entry MSB
                        if rpn_state[channel]['RPN_MSB'] == 0 and rpn_state[channel]['RPN_LSB'] == 0:
                            pitch_bend_range[channel] = float(cc_value)
                    elif cc_number == 38:  # Data Entry LSB (fine tuning)
                        if rpn_state[channel]['RPN_MSB'] == 0 and rpn_state[channel]['RPN_LSB'] == 0:
                            rpn_state[channel]['fine'] = cc_value
                            pitch_bend_range[channel] = float(pitch_bend_range[channel]) + (cc_value / 100.0)
                    elif cc_number in (101, 100) and cc_value == 127:  # Reset RPN
                        rpn_state[channel]['RPN_MSB'] = None
                        rpn_state[channel]['RPN_LSB'] = None

                    # Handle common CC events that affect audio output
                    elif cc_number in [1, 7, 10, 11, 39, 43, 71, 74]:  # Modulation, Volume, Pan, Expression, Volume LSB, Expression LSB, Resonance, Cutoff
                        yield MIDIEvent(time, 'control_change', channel,
                                      cc_number=cc_number, cc_value=cc_value)

                    # Handle Channel Mode Messages (CC 120-127)
                    elif cc_number in [120, 123]:  # All Sound Off, All Notes Off
                        yield MIDIEvent(time, 'control_change', channel,
                                      cc_number=cc_number, cc_value=cc_value)

                elif event_type == 0xE0:  # Pitch bend
                    # Pitch bend is a 14-bit value, centered at 8192 (no bend)
                    lsb, msb = data[offset], data[offset+1]
                    offset += 2
                    bend_value = (msb << 7) + lsb
                    range_semitones = pitch_bend_range.get(channel, 2.0)
                    semitone_offset = ((bend_value - 8192) / 8192) * range_semitones
                    channel_pitch_bend[channel] = semitone_offset

                    yield MIDIEvent(time, 'pitch_bend', channel,
                                    pitch_bend=semitone_offset)
                else:
                    param1, param2 = data[offset], data[offset+1]
                    offset += 2

                    if event_type == 0x90 and param2 > 0:  # Note on
                        yield MIDIEvent(time, 'note_on', channel, param1, param2,
                                        pitch_bend=channel_pitch_bend[channel])
                    elif event_type == 0x80 or (event_type == 0x90 and param2 == 0):  # Note off
                        yield MIDIEvent(time, 'note_off', channel, param1, param2,
                                        pitch_bend=channel_pitch_bend[channel])
            
            elif event_type in [0xC0, 0xD0]:
                param1 = data[offset]
                offset += 1

                if event_type == 0xC0:  # Program change
                    yield MIDIEvent(time, 'program_change', channel, param1)
                elif event_type == 0xD0:  # Channel pressure (aftertouch)
                    yield MIDIEvent(time, 'channel_pressure', channel, param1)


def parse_midi_file(filename: str, progress_callback=None) -> Tuple[int, int, List[bytes], List[Tuple[int, int]]]:
    with open(filename, 'rb') as file:
        if file.read(4) != b'MThd':
            raise ValueError("Not a valid MIDI file")
        
        header_length = struct.unpack('>I', file.read(4))[0]
        format_type, num_tracks, ticks_per_quarter_note = struct.unpack('>HHH', file.read(6))
        
        tracks = []
        all_tempo_changes = []
        
        for i in range(num_tracks):
            if progress_callback:
                progress_callback("Loading MIDI tracks", i, num_tracks)
                
            if file.read(4) != b'MTrk':
                raise ValueError(f"Track {i} is not valid")
            
            track_length = struct.unpack('>I', file.read(4))[0]
            track_data = file.read(track_length)
            tracks.append(track_data)
            
            # Extract tempo changes from each track
            track_tempo_changes = []
            for event in parse_midi_events(track_data):
                if event.event_type == 'set_tempo':
                    track_tempo_changes.append((event.time, event.param1))
            all_tempo_changes.extend(track_tempo_changes)

        # Sort and merge all tempo changes
        all_tempo_changes.sort(key=lambda x: x[0])
        merged_tempo_changes = []
        for time, tempo in all_tempo_changes:
            if not merged_tempo_changes or merged_tempo_changes[-1][1] != tempo:
                merged_tempo_changes.append((time, tempo))

        if not merged_tempo_changes:
            merged_tempo_changes = [(0, 500000)]  # Default tempo at the start (120 BPM)

        return ticks_per_quarter_note, num_tracks, tracks, merged_tempo_changes

def load_samples(sf2_path: str, progress_callback=None, disable_release_fade: bool = False,
                release_time: float = 0.1, target_sample_rate: int = 44100,
                channel_programs: Dict[int, int] = None,
                required_notes: Set[Tuple[int, int]] = None) -> Dict[Tuple[int, int], Tuple[np.ndarray, int, dict]]:
    """
    Load samples from a SoundFont file using the SF2NewParser.
    Uses the improved sample loading logic to handle stereo samples and proper pitch mapping.
    Now supports multi-instrument loading for different MIDI channels.
    Optimized to only load samples that are actually needed to reduce memory usage.

    Args:
        sf2_path: Path to the SoundFont (.sf2) file
        progress_callback: Optional callback function for progress reporting
        disable_release_fade: If False, pre-process samples with fade-out for optimization
        release_time: Release time in seconds for fade-out pre-processing
        target_sample_rate: Target sample rate for fade-out calculations
        channel_programs: Optional dictionary mapping MIDI channels to GM program numbers
        required_notes: Optional set of (channel, note) tuples to load. If None, loads all samples.

    Returns:
        Dictionary mapping (channel, note) tuples to (sample_data, sample_rate, loop_info) tuples
    """
    try:
        if progress_callback:
            progress_callback("Initializing SF2 parser", 0, 100)

        parser = SF2NewParser(sf2_path)

        if progress_callback:
            progress_callback("Parsing SF2 structure (without loading sample data)", 10, 100)

        # Parse the SF2 structure but don't load sample data yet
        # We'll modify the parser to skip automatic sample loading
        with open(sf2_path, 'rb') as f:
            parser.file = f
            parser._parse_riff_header()
            if parser.chunks:
                parser._load_pdta_data()
            else:
                if progress_callback:
                    progress_callback("Warning: No data chunks found in the file.", 100, 100)
                return {}

        if progress_callback:
            progress_callback("SF2 structure parsed, sample data loading deferred", 20, 100)

        if progress_callback:
            progress_callback(f"Found {len(parser.sample_headers)} samples", 20, 100)
            progress_callback(f"Found {len(parser.presets)} presets", 25, 100)
            progress_callback(f"Found {len(parser.instruments)} instruments", 30, 100)

        # Create a custom sample loader that only loads what we need and handles looping
        def load_specific_sample_with_loops(sample_id: int) -> Optional[np.ndarray]:
            """Load only a specific sample from the SF2 file with proper loop handling."""
            if sample_id >= len(parser.sample_headers):
                return None

            header = parser.sample_headers[sample_id]

            # Load only the sample data we need from the file
            if b'smpl' not in parser.chunks:
                return None

            smpl_chunk = parser.chunks[b'smpl']

            try:
                with open(sf2_path, 'rb') as f:
                    # Calculate the byte offset for this specific sample
                    sample_start_byte = smpl_chunk.data_offset + (header.start * 2)  # 2 bytes per sample (int16)
                    sample_length = header.end - header.start
                    sample_size_bytes = sample_length * 2

                    # Seek to the specific sample location
                    f.seek(sample_start_byte)

                    # Read only this sample's data
                    sample_bytes = f.read(sample_size_bytes)

                    if len(sample_bytes) < sample_size_bytes:
                        return None

                    # Convert to numpy array and normalize
                    raw_data = np.frombuffer(sample_bytes, dtype=np.int16)
                    float_data = raw_data.astype(np.float32) / 32768.0

                    # Handle looping - extend ONLY short samples that HAVE loops to 7 seconds
                    duration_seconds = len(float_data) / header.sample_rate if header.sample_rate > 0 else 0
                    target_duration = 7.0  # 7 seconds target for short samples with loops

                    # Only extend if sample is short AND has loop information
                    if duration_seconds < target_duration and (header.sample_type & 1) and header.loop_start < header.loop_end:
                        target_length = int(header.sample_rate * target_duration)

                        # Calculate loop points relative to the sample start
                        loop_start = max(0, min(header.loop_start - header.start, len(float_data) - 1))
                        loop_end = max(loop_start + 1, min(header.loop_end - header.start, len(float_data)))

                        if loop_end > loop_start + 1:  # Valid loop found
                            loop_section = float_data[loop_start:loop_end]

                            if len(loop_section) > 0:
                                # Calculate how many loop repetitions we need to reach 7 seconds
                                loops_needed = max(1, (target_length - len(float_data)) // len(loop_section))

                                # Create extended sample
                                attack_section = float_data[:loop_start] if loop_start > 0 else np.array([])
                                release_section = float_data[loop_end:] if loop_end < len(float_data) else np.array([])

                                # Repeat the loop section to fill 7 seconds
                                extended_loop = np.tile(loop_section, loops_needed)

                                # Combine sections
                                parts = []
                                if len(attack_section) > 0:
                                    parts.append(attack_section)
                                parts.append(extended_loop)
                                if len(release_section) > 0:
                                    parts.append(release_section)

                                if parts:
                                    float_data = np.concatenate(parts)

                                # Trim to exactly 7 seconds if we went over
                                if len(float_data) > target_length:
                                    float_data = float_data[:target_length]

                    return float_data

            except Exception as e:
                print(f"Error loading specific sample {sample_id}: {e}")
                return None

        def get_sample_loop_info_custom(sample_id: int) -> dict:
            """Get loop information for a specific sample without loading all sample data."""
            if sample_id >= len(parser.sample_headers):
                return {'has_loop': False, 'loop_start': 0, 'loop_end': 0, 'loop_length': 0}

            header = parser.sample_headers[sample_id]

            # Check if sample has loop information
            has_loop = bool(header.sample_type & 1) and header.loop_start < header.loop_end

            if has_loop:
                # Calculate loop points relative to the sample start
                loop_start = max(0, header.loop_start - header.start)
                loop_end = min(header.end - header.start, header.loop_end - header.start)
                loop_length = loop_end - loop_start

                return {
                    'has_loop': True,
                    'loop_start': loop_start,
                    'loop_end': loop_end,
                    'loop_length': loop_length
                }
            else:
                return {'has_loop': False, 'loop_start': 0, 'loop_end': 0, 'loop_length': 0}

        # Build the samples dictionary
        # The key is now (channel, note) tuple for multi-instrument support
        samples = {}

        # Create channel to preset mapping
        if progress_callback:
            progress_callback("Creating channel to preset mapping", 35, 100)

        channel_preset_mapping = parser.create_channel_preset_mapping(channel_programs)

        if progress_callback:
            progress_callback(f"Mapped {len(channel_preset_mapping)} channels to presets", 40, 100)

        # Load samples for each channel's preset
        preset_mapping_success = False
        total_successful_samples = 0

        if len(parser.presets) > 0:
            # Process each channel's preset
            for channel, preset_index in channel_preset_mapping.items():
                if preset_index >= len(parser.presets):
                    continue

                # Skip this channel entirely if no notes are required for it
                if required_notes is not None:
                    channel_has_required_notes = any(ch == channel for ch, note in required_notes)
                    if not channel_has_required_notes:
                        continue

                preset = parser.presets[preset_index]
                if progress_callback:
                    progress_percentage = 45 + (channel * 40 // 16)
                    progress_callback(f"Loading samples for channel {channel} (preset: {preset.name})",
                                     progress_percentage, 100)

                # Get sample mappings for this preset
                sample_map = parser.get_samples_for_preset(preset_index)

                if sample_map:
                    # Process samples for this channel
                    processed_samples = 0
                    total_samples = len(sample_map)

                    for note, (sample_id, generators) in sample_map.items():
                        # Skip this note if we have specific requirements and this note isn't needed
                        if required_notes is not None and (channel, note) not in required_notes:
                            continue

                        try:
                            # Use our custom loader to only load the specific sample we need
                            sample_data = load_specific_sample_with_loops(sample_id)

                            if sample_data is not None:
                                # Get the sample header for pitch information
                                header = parser.sample_headers[sample_id]

                                # Note: Removed sample duration filtering - let the loop system handle short samples

                                # Check for very quiet samples (might indicate parsing issues)
                                max_amplitude = np.max(np.abs(sample_data))
                                if max_amplitude < 0.001:  # Very quiet sample
                                    print(f"Warning: Very quiet sample for channel {channel}, note {note}: "
                                          f"max amplitude {max_amplitude:.6f}")

                                # Safety check: ensure sample is 2D (stereo)
                                if len(sample_data.shape) == 1:
                                    sample_data = np.column_stack((sample_data, sample_data))

                                # Modify the sample for playback (pitch shifting, etc.)
                                modified_sample = parser.modify_sample_for_playback(sample_data, header, generators, note)

                                if modified_sample is not None:
                                    # Ensure the modified sample is still stereo
                                    if len(modified_sample.shape) == 1:
                                        modified_sample = np.column_stack((modified_sample, modified_sample))

                                    # Debug: Check sample duration after processing
                                    sample_duration = len(modified_sample) / header.sample_rate
                                    loop_info = get_sample_loop_info_custom(sample_id)

                                    if sample_duration >= 7.0 and loop_info and loop_info['has_loop']:
                                        print(f"Info: Extended looped sample for channel {channel}, note {note}: "
                                              f"{len(modified_sample)} samples ({sample_duration:.1f}s) - extended to 7 seconds")
                                    elif sample_duration < 0.1 and loop_info and loop_info['has_loop']:
                                        print(f"Info: Short looped sample for channel {channel}, note {note}: "
                                              f"{len(modified_sample)} samples ({sample_duration*1000:.1f}ms) - will loop properly")
                                    elif sample_duration < 0.05:  # Very short non-looped samples
                                        print(f"Warning: Very short sample for channel {channel}, note {note}: "
                                              f"{len(modified_sample)} samples ({sample_duration*1000:.1f}ms) - may sound like tapping")

                                    # Get loop information for this sample
                                    loop_info = get_sample_loop_info_custom(sample_id)
                                    if not loop_info:
                                        loop_info = {'has_loop': False, 'loop_start': 0, 'loop_end': 0, 'loop_length': 0}

                                    # Store sample with (channel, note) key including loop info
                                    samples[(channel, note)] = (modified_sample, header.sample_rate, loop_info)
                                    total_successful_samples += 1

                            processed_samples += 1

                        except Exception as e:
                            if progress_callback and processed_samples % 10 == 0:
                                progress_callback(f"Warning: Failed to process channel {channel} note {note}: {str(e)}",
                                                 45 + (channel * 40 // 16), 100)
                            continue

                    if processed_samples > 0:
                        preset_mapping_success = True

        # If preset mapping was unsuccessful or no presets were found, fall back to direct sample loading
        if not preset_mapping_success:
            if progress_callback:
                progress_callback("Preset mapping failed, falling back to direct sample loading", 85, 100)

            # Pre-filter samples to only process those we actually need
            if required_notes is not None:
                required_note_numbers = set(note for ch, note in required_notes)
                samples_to_process = []
                for i, sample_header in enumerate(parser.sample_headers):
                    note = sample_header.original_pitch
                    if 0 <= note <= 127 and note in required_note_numbers:
                        samples_to_process.append((i, sample_header))
                if progress_callback:
                    progress_callback(f"Pre-filtered to {len(samples_to_process)} samples out of {len(parser.sample_headers)} total", 87, 100)
            else:
                samples_to_process = list(enumerate(parser.sample_headers))

            sample_count = len(samples_to_process)
            for idx, (i, sample_header) in enumerate(samples_to_process):
                if progress_callback and idx % max(1, sample_count // 20) == 0:
                    progress_percentage = 87 + (idx * 8 // sample_count)
                    progress_callback(f"Processing sample {idx+1}/{sample_count}", progress_percentage, 100)

                try:
                    # Use our custom loader to only load the specific sample we need
                    sample_data = load_specific_sample_with_loops(i)

                    if sample_data is not None:
                        # Ensure sample is stereo
                        if len(sample_data.shape) == 1:
                            sample_data = np.column_stack((sample_data, sample_data))

                        # Get loop information for this sample
                        loop_info = get_sample_loop_info_custom(i)

                        # Store sample for required channels/notes only
                        note = sample_header.original_pitch
                        if 0 <= note <= 127:  # Verify note is in valid MIDI range
                            if required_notes is None:
                                # Load for all channels if no specific requirements
                                for channel in range(16):
                                    samples[(channel, note)] = (sample_data, sample_header.sample_rate, loop_info)
                                total_successful_samples += 1
                            else:
                                # Only load for required (channel, note) combinations
                                loaded_any = False
                                for channel in range(16):
                                    if (channel, note) in required_notes:
                                        samples[(channel, note)] = (sample_data, sample_header.sample_rate, loop_info)
                                        loaded_any = True
                                if loaded_any:
                                    total_successful_samples += 1

                except Exception as e:
                    if progress_callback and i % 10 == 0:
                        progress_callback(f"Warning: Failed to process sample {i} ({sample_header.name}): {str(e)}",
                                         85 + (i * 10 // sample_count), 100)
                    continue

        # Final check: ensure we have at least some samples
        if not samples:
            # Last resort: just load samples directly without any mapping
            if progress_callback:
                progress_callback("All previous methods failed, loading raw samples", 95, 100)

            # Pre-filter samples to only process those we actually need
            if required_notes is not None:
                required_note_numbers = set(note for ch, note in required_notes)
                samples_to_process = []
                for i, sample_header in enumerate(parser.sample_headers):
                    note = sample_header.original_pitch
                    if 0 <= note <= 127 and note in required_note_numbers:
                        samples_to_process.append((i, sample_header))
                if progress_callback:
                    progress_callback(f"Final fallback: processing {len(samples_to_process)} samples out of {len(parser.sample_headers)} total", 96, 100)
            else:
                samples_to_process = list(enumerate(parser.sample_headers))

            for i, sample_header in samples_to_process:
                try:
                    # Use our custom loader to only load the specific sample we need
                    raw_sample = load_specific_sample_with_loops(i)
                    if raw_sample is not None:
                        if len(raw_sample.shape) == 1:
                            raw_sample = np.column_stack((raw_sample, raw_sample))

                        # Get loop information for this sample
                        loop_info = get_sample_loop_info_custom(i)

                        note = sample_header.original_pitch
                        if 0 <= note <= 127:
                            if required_notes is None:
                                # Load for all channels if no specific requirements
                                for channel in range(16):
                                    samples[(channel, note)] = (raw_sample, sample_header.sample_rate, loop_info)
                            else:
                                # Only load for required (channel, note) combinations
                                for channel in range(16):
                                    if (channel, note) in required_notes:
                                        samples[(channel, note)] = (raw_sample, sample_header.sample_rate, loop_info)
                except Exception:
                    continue

        # Pre-process samples with fade-out if enabled (when disable_release_fade is False)
        if not disable_release_fade and samples:
            if progress_callback:
                progress_callback("Pre-processing samples with fade-out...", 97, 100)

            processed_samples = {}
            for (channel, note), (sample_data, sample_rate, loop_info) in samples.items():
                # Apply fade-out to the end of each sample
                processed_sample = apply_fadeout_to_sample(sample_data, release_time, sample_rate)
                processed_samples[(channel, note)] = (processed_sample, sample_rate, loop_info)

            samples = processed_samples

        if progress_callback:
            if samples:
                # Show statistics about loaded samples
                channels_with_samples = set(channel for channel, note in samples.keys())
                unique_notes = set(note for channel, note in samples.keys())
                min_note = min(unique_notes) if unique_notes else 0
                max_note = max(unique_notes) if unique_notes else 0
                fade_status = "with pre-processed fade-out" if not disable_release_fade else "without fade-out pre-processing"
                progress_callback(f"Successfully loaded {len(samples)} samples across {len(channels_with_samples)} channels (note range: {min_note}-{max_note}) {fade_status}", 100, 100)
            else:
                progress_callback("Failed to load any usable samples from this SoundFont", 100, 100)

        return samples
        
    except Exception as e:
        if progress_callback:
            progress_callback(f"Error loading SF2 file: {str(e)}", 100, 100)
        import traceback
        if progress_callback:
            progress_callback(traceback.format_exc(), 100, 100)
        print(traceback.format_exc())  # Print stack trace to console as well
        return {}

def load_samples_single_instrument(sf2_path: str, progress_callback=None, disable_release_fade: bool = False,
                                  release_time: float = 0.1, target_sample_rate: int = 44100) -> Dict[int, Tuple[np.ndarray, int, dict]]:
    """
    Backward compatibility function that loads samples for a single instrument (piano).

    Args:
        sf2_path: Path to the SoundFont (.sf2) file
        progress_callback: Optional callback function for progress reporting
        disable_release_fade: If False, pre-process samples with fade-out for optimization
        release_time: Release time in seconds for fade-out pre-processing
        target_sample_rate: Target sample rate for fade-out calculations

    Returns:
        Dictionary mapping MIDI note numbers to (sample_data, sample_rate, loop_info) tuples
    """
    # Load multi-instrument samples with default piano mapping
    multi_samples = load_samples(sf2_path, progress_callback, disable_release_fade, release_time, target_sample_rate)

    # Convert to single-instrument format by using channel 0 samples
    single_samples = {}
    for (channel, note), (sample_data, sample_rate, loop_info) in multi_samples.items():
        if channel == 0:  # Use piano channel
            single_samples[note] = (sample_data, sample_rate, loop_info)

    return single_samples

def analyze_midi_programs(midi_path: str) -> Dict[int, int]:
    """
    Analyze a MIDI file to extract program change information for each channel.
    Channel 10 (index 9) is always treated as drums regardless of program changes.

    Args:
        midi_path: Path to the MIDI file

    Returns:
        Dictionary mapping MIDI channels to their last program change (GM program number)
        Channel 9 (drums) will have a special value to indicate drum channel
    """
    try:
        _, _, tracks, _ = parse_midi_file(midi_path)

        # Track the current program for each channel (default to 0 = Acoustic Grand Piano)
        channel_programs = {i: 0 for i in range(16)}

        # Track which channels have note events to identify active channels
        active_channels = set()

        # Parse all tracks to find program change events and note events
        for track in tracks:
            for event in parse_midi_events(track):
                if event.event_type == 'program_change' and event.channel != 9:
                    # Don't apply program changes to channel 10 (drums)
                    channel_programs[event.channel] = event.param1
                elif event.event_type in ['note_on', 'note_off']:
                    active_channels.add(event.channel)

        # Channel 10 (index 9) is always drums in General MIDI
        # Use a special marker value that the SF2 parser will recognize
        if 9 in active_channels:
            channel_programs[9] = -1  # Special marker for drum channel
        else:
            channel_programs[9] = 0   # No drum activity, treat as regular instrument

        return channel_programs

    except Exception as e:
        print(f"Error analyzing MIDI programs: {e}")
        # Return default mapping
        default_programs = {i: 0 for i in range(16)}
        default_programs[9] = -1  # Mark as drum channel
        return default_programs

def analyze_midi_notes_used(midi_path: str) -> Set[Tuple[int, int]]:
    """
    Analyze a MIDI file to extract which specific (channel, note) combinations are actually used.
    This allows for selective sample loading to reduce memory usage.

    Args:
        midi_path: Path to the MIDI file

    Returns:
        Set of (channel, note) tuples that are actually used in the MIDI file
    """
    try:
        _, _, tracks, _ = parse_midi_file(midi_path)

        used_notes = set()

        # Parse all tracks to find note events
        for track in tracks:
            for event in parse_midi_events(track):
                if event.event_type == 'note_on' and event.param2 > 0:  # Note on with velocity > 0
                    used_notes.add((event.channel, event.param1))
                elif event.event_type == 'note_off':
                    used_notes.add((event.channel, event.param1))

        return used_notes

    except Exception as e:
        print(f"Error analyzing MIDI notes: {e}")
        return set()  # Return empty set on error

def tick2second(tick: int, ticks_per_beat: int, tempo: int) -> float:
    return (tick * tempo) / (ticks_per_beat * 1000000)

def scale_velocity(velocity: int) -> float:
    velocity = max(0, min(127, velocity))
    scaled = np.log(velocity + 1) / np.log(128)
    curved = np.power(scaled, 16)
    normalized = (curved - 0) / (1 - 0)
    return normalized

# Cache for pre-computed release envelopes to avoid repeated computation
_release_envelope_cache = {}
_max_cache_size = 1000

# Pre-computed lookup table for common envelope sizes (power of 2 sizes up to 64k samples)
_common_envelope_sizes = [64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 65536]
_precomputed_envelopes = {}

@njit(fastmath=True, cache=True)
def _create_release_envelope_numba(length: int, release_samples: int) -> np.ndarray:
    """Numba-optimized release envelope creation"""
    if length <= release_samples:
        # Create linear fade from 1.0 to 0.0 for entire length
        envelope = np.empty(length, dtype=np.float32)
        for i in range(length):
            envelope[i] = 1.0 - (i / (length - 1)) if length > 1 else 0.0
        return envelope
    else:
        # Create envelope with ones, then linear fade at the end
        envelope = np.ones(length, dtype=np.float32)
        fade_start = length - release_samples
        for i in range(release_samples):
            envelope[fade_start + i] = 1.0 - (i / (release_samples - 1)) if release_samples > 1 else 0.0
        return envelope

def _initialize_precomputed_envelopes(release_time: float, sample_rate: int):
    """Initialize pre-computed envelopes for common sizes"""
    global _precomputed_envelopes
    release_samples = int(release_time * sample_rate)

    for size in _common_envelope_sizes:
        if size not in _precomputed_envelopes:
            _precomputed_envelopes[size] = {}
        if release_samples not in _precomputed_envelopes[size]:
            _precomputed_envelopes[size][release_samples] = _create_release_envelope_numba(size, release_samples)

def create_release_envelope(length: int, release_time: float, sample_rate: int) -> np.ndarray:
    """Creates a linear release envelope with caching and pre-computed lookup for performance"""
    release_samples = int(release_time * sample_rate)

    # Check pre-computed envelopes first for common sizes
    if length in _precomputed_envelopes and release_samples in _precomputed_envelopes[length]:
        return _precomputed_envelopes[length][release_samples].copy()

    # Create cache key for less common sizes
    cache_key = (length, release_samples)

    # Check cache
    if cache_key in _release_envelope_cache:
        return _release_envelope_cache[cache_key].copy()

    # Clear cache if it gets too large
    if len(_release_envelope_cache) >= _max_cache_size:
        # Keep only the most recently used half
        keys_to_remove = list(_release_envelope_cache.keys())[:-_max_cache_size//2]
        for key in keys_to_remove:
            del _release_envelope_cache[key]

    # Create new envelope using Numba-optimized function
    envelope = _create_release_envelope_numba(length, release_samples)

    # Cache the result for non-common sizes
    if length not in _common_envelope_sizes:
        _release_envelope_cache[cache_key] = envelope.copy()

    return envelope

@njit(fastmath=True, cache=True)
def _apply_release_envelope_numba(audio_segment: np.ndarray, envelope: np.ndarray, velocity: float) -> np.ndarray:
    """Numba-optimized envelope application for both mono and stereo"""
    if audio_segment.ndim == 1:
        # Mono case
        result = np.empty_like(audio_segment)
        for i in range(len(audio_segment)):
            result[i] = audio_segment[i] * envelope[i] * velocity
        return result
    else:
        # Stereo case
        result = np.empty_like(audio_segment)
        for i in range(len(audio_segment)):
            for ch in range(audio_segment.shape[1]):
                result[i, ch] = audio_segment[i, ch] * envelope[i] * velocity
        return result

def apply_release_envelope_optimized(audio_segment: np.ndarray, segment_length: int,
                                   release_time: float, sample_rate: int, velocity: float) -> np.ndarray:
    """Optimized release envelope application with caching and Numba acceleration"""
    if segment_length <= 0:
        return np.zeros_like(audio_segment[:0])

    # Get cached envelope
    envelope = create_release_envelope(segment_length, release_time, sample_rate)

    # Apply envelope using Numba-optimized function
    return _apply_release_envelope_numba(audio_segment[:segment_length], envelope, velocity)

def apply_fadeout_to_sample(sample_data: np.ndarray, release_time: float, sample_rate: int) -> np.ndarray:
    """
    Pre-process a sample by applying fade-out to the end.
    This is done once during sample loading instead of during rendering for optimization.
    """
    if len(sample_data) == 0:
        return sample_data

    # Calculate fade-out length in samples
    fade_samples = int(release_time * sample_rate)

    # If the sample is shorter than the fade time, fade the entire sample
    if len(sample_data) <= fade_samples:
        fade_envelope = np.linspace(1.0, 0.0, len(sample_data))
    else:
        # Create fade envelope that only affects the end of the sample
        fade_envelope = np.ones(len(sample_data))
        fade_envelope[-fade_samples:] = np.linspace(1.0, 0.0, fade_samples)

    # Apply fade envelope to all channels
    if sample_data.ndim == 1:
        # Mono sample
        return sample_data * fade_envelope
    else:
        # Stereo sample - apply fade to both channels
        fade_envelope = fade_envelope.reshape(-1, 1)
        return sample_data * fade_envelope

def preprocess_sample_with_fadeout(sample_data: np.ndarray, release_time: float, sample_rate: int) -> np.ndarray:
    """
    Pre-process a sample by applying fade-out at the end for optimization.
    This eliminates the need to apply fade envelopes during rendering.
    Uses optimized vectorized operations for maximum speed.

    Args:
        sample_data: The sample data to process
        release_time: Release time in seconds
        sample_rate: Sample rate for calculating fade length

    Returns:
        Sample data with fade-out pre-applied
    """
    if len(sample_data) == 0:
        return sample_data

    # Calculate fade-out length in samples
    fade_samples = int(release_time * sample_rate)

    # Early return if no fade needed
    if fade_samples <= 0:
        return sample_data

    # Create a copy to avoid modifying the original
    result = sample_data.copy()

    # If the sample is shorter than the fade time, fade the entire sample
    if len(sample_data) <= fade_samples:
        # Use optimized linspace for the entire sample
        fade_envelope = np.linspace(1.0, 0.0, len(sample_data), dtype=np.float32)
        if sample_data.ndim == 1:
            result *= fade_envelope
        else:
            # Broadcasting for multi-channel
            result *= fade_envelope[:, np.newaxis]
    else:
        # Apply fade-out only to the end portion - more memory efficient
        fade_start = len(sample_data) - fade_samples
        fade_envelope = np.linspace(1.0, 0.0, fade_samples, dtype=np.float32)

        if sample_data.ndim == 1:
            result[fade_start:] *= fade_envelope
        else:
            # Broadcasting for multi-channel - more efficient than reshape
            result[fade_start:] *= fade_envelope[:, np.newaxis]

    return result

def quantize_time(time_sec: float, processing_fps: int, fps_fluctuation: float) -> float:
    """Quantizes a time value based on FPS and fluctuation."""
    if processing_fps <= 0:
        return time_sec
    
    factor = 1.0
    if fps_fluctuation > 0:
        # Ensure fluctuation doesn't result in zero or negative FPS
        min_factor = 1.0 / (1.0 + fps_fluctuation) if fps_fluctuation < 1 else 0.01
        max_factor = 1.0 + fps_fluctuation
        factor = random.uniform(min_factor, max_factor)
        
    effective_fps = processing_fps * factor
    if effective_fps <= 0: # Avoid division by zero
        return time_sec
        
    dt = 1.0 / effective_fps
    return round(time_sec / dt) * dt
    
def process_track_chunk(track_data: bytes, samples: Dict[Tuple[int, int], Tuple[np.ndarray, int, dict]],
                       ticks_per_beat: int, tempo_changes: List[Tuple[int, int]],
                       sample_rate: int, chunk_start_time: float, chunk_end_time: float,
                       polyphony_limit: int, release_time: float,
                       processing_fps: int, fps_fluctuation: float, # Added
                       disable_release_fade: bool = False) -> np.ndarray:
    events = list(parse_midi_events(track_data))
    
    if not events:
        return np.zeros((int((chunk_end_time - chunk_start_time) * sample_rate), 2), dtype=np.float32)

    chunk_duration = chunk_end_time - chunk_start_time
    output = np.zeros((int(chunk_duration * sample_rate), 2), dtype=np.float32)

    active_notes = {}
    next_note_id = 0
    note_ids = {}  # Maps (channel, note) to list of active note IDs
    channel_notes_count = {i: 0 for i in range(16)}  # Track active notes per channel
    channel_pitch_bend = {i: 0.0 for i in range(16)}

    # CC state tracking for each channel
    channel_cc_state = {}
    for ch in range(16):
        channel_cc_state[ch] = {
            'volume': 100,      # CC 7 - Channel Volume (0-127, default 100)
            'volume_lsb': 0,    # CC 39 - Volume LSB (0-127, default 0)
            'pan': 64,          # CC 10 - Pan (0=left, 64=center, 127=right)
            'expression': 127,  # CC 11 - Expression (0-127, default 127)
            'expression_lsb': 0, # CC 43 - Expression LSB (0-127, default 0)
            'modulation': 0,    # CC 1 - Modulation Wheel (0-127, default 0)
            'resonance': 64,    # CC 71 - Resonance/Filter (0-127, default 64)
            'cutoff': 127       # CC 74 - Frequency Cutoff/Brightness (0-127, default 127)
        }

    current_tempo_index = 0
    current_time = 0

    def get_current_time(event_tick):
        nonlocal current_tempo_index, current_time
        while current_tempo_index < len(tempo_changes) - 1 and event_tick >= tempo_changes[current_tempo_index + 1][0]:
            next_tempo_tick, next_tempo = tempo_changes[current_tempo_index + 1]
            current_time += tick2second(next_tempo_tick - tempo_changes[current_tempo_index][0], 
                                     ticks_per_beat, tempo_changes[current_tempo_index][1])
            current_tempo_index += 1
        
        remaining_ticks = event_tick - tempo_changes[current_tempo_index][0]
        return current_time + tick2second(remaining_ticks, ticks_per_beat, tempo_changes[current_tempo_index][1])

    def resample_segment(segment: np.ndarray, output_length: int) -> np.ndarray:
        """Resample a segment to a different length using linear interpolation (from midi_loader.py)"""
        if len(segment) == 0:
            return np.zeros((output_length, segment.shape[1]), dtype=segment.dtype)
        x_old = np.linspace(0, 1, num=len(segment))
        x_new = np.linspace(0, 1, num=output_length)
        resampled = []
        for ch in range(segment.shape[1]):
            resampled_channel = np.interp(x_new, x_old, segment[:, ch])
            resampled.append(resampled_channel)
        return np.stack(resampled, axis=-1).astype(segment.dtype)

    def apply_pitch_bend_to_segment(original_sample: np.ndarray, input_offset: int,
                                   output_samples: int, semitone_offset: float,
                                   original_sample_rate: int = 44100, target_sample_rate: int = 44100) -> np.ndarray:
        """Apply pitch bending and sample rate conversion using proper resampling"""
        # Calculate combined pitch and sample rate factor
        pitch_factor = 2 ** (semitone_offset / 12.0)
        rate_factor = original_sample_rate / target_sample_rate
        combined_factor = pitch_factor * rate_factor

        if abs(combined_factor - 1.0) < 0.01:
            # No significant pitch bend or rate conversion, just take the segment directly
            end_offset = min(input_offset + output_samples, len(original_sample))
            segment = original_sample[input_offset:end_offset]
            if len(segment) < output_samples:
                # Pad with zeros if we don't have enough samples
                pad_width = output_samples - len(segment)
                segment = np.concatenate([segment, np.zeros((pad_width, segment.shape[1]), dtype=segment.dtype)], axis=0)
            return segment

        # Calculate required input samples for both pitch shift and sample rate conversion
        input_samples_needed = int(output_samples * combined_factor)

        # Extract the input segment
        end_offset = min(input_offset + input_samples_needed, len(original_sample))
        input_segment = original_sample[input_offset:end_offset]

        # Pad with zeros if we don't have enough input samples
        if len(input_segment) < input_samples_needed:
            pad_width = input_samples_needed - len(input_segment)
            input_segment = np.concatenate([input_segment, np.zeros((pad_width, input_segment.shape[1]), dtype=input_segment.dtype)], axis=0)

        # Resample to the desired output length (handles both pitch shift and sample rate conversion)
        return resample_segment(input_segment, output_samples)

    def apply_pitch_bend_with_looping(original_sample: np.ndarray, input_offset: int,
                                     output_samples: int, semitone_offset: float,
                                     original_sample_rate: int = 44100, target_sample_rate: int = 44100,
                                     loop_info: dict = None, is_releasing: bool = False) -> Tuple[np.ndarray, int]:
        """Apply pitch bending with proper SF2 loop support, returns (audio, new_input_offset)"""

        # Only use loop extension during sustain phase (not during release)
        if not loop_info or not loop_info['has_loop'] or is_releasing:
            result = apply_pitch_bend_to_segment(original_sample, input_offset, output_samples,
                                               semitone_offset, original_sample_rate, target_sample_rate)
            # Calculate new input offset
            pitch_factor = 2 ** (semitone_offset / 12.0)
            rate_factor = original_sample_rate / target_sample_rate
            combined_factor = pitch_factor * rate_factor
            input_consumed = int(output_samples * combined_factor)
            new_input_offset = min(input_offset + input_consumed, len(original_sample))
            return result, new_input_offset

        # Handle SF2 looping
        loop_start = loop_info['loop_start']
        loop_end = loop_info['loop_end']
        loop_length = loop_end - loop_start
        sample_length = len(original_sample)

        # Validate loop points
        if loop_length <= 0 or loop_start >= sample_length or loop_end > sample_length:
            result = apply_pitch_bend_to_segment(original_sample, input_offset, output_samples,
                                               semitone_offset, original_sample_rate, target_sample_rate)
            pitch_factor = 2 ** (semitone_offset / 12.0)
            rate_factor = original_sample_rate / target_sample_rate
            combined_factor = pitch_factor * rate_factor
            input_consumed = int(output_samples * combined_factor)
            new_input_offset = min(input_offset + input_consumed, len(original_sample))
            return result, new_input_offset

        # Calculate pitch and rate factors
        pitch_factor = 2 ** (semitone_offset / 12.0)
        rate_factor = original_sample_rate / target_sample_rate
        combined_factor = pitch_factor * rate_factor

        # Calculate how many input samples we need
        input_samples_needed = int(output_samples * combined_factor)

        # Generate input samples with proper looping
        # Handle both mono and stereo samples
        if len(original_sample.shape) == 1:
            input_segment = np.zeros(input_samples_needed, dtype=original_sample.dtype)
        else:
            input_segment = np.zeros((input_samples_needed, original_sample.shape[1]), dtype=original_sample.dtype)

        current_input_pos = input_offset
        samples_generated = 0

        while samples_generated < input_samples_needed:
            if current_input_pos < loop_start:
                # Attack phase: play from current position to loop start
                attack_samples_available = loop_start - current_input_pos
                attack_samples_needed = min(attack_samples_available, input_samples_needed - samples_generated)

                if attack_samples_needed > 0:
                    attack_segment = original_sample[current_input_pos:current_input_pos + attack_samples_needed]
                    input_segment[samples_generated:samples_generated + attack_samples_needed] = attack_segment
                    samples_generated += attack_samples_needed
                    current_input_pos += attack_samples_needed

            elif current_input_pos >= loop_start and current_input_pos < loop_end:
                # Sustain phase: loop the loop section
                remaining_samples = input_samples_needed - samples_generated

                # Calculate position within the loop
                loop_pos = (current_input_pos - loop_start) % loop_length
                loop_samples_available = loop_length - loop_pos
                loop_samples_to_copy = min(loop_samples_available, remaining_samples)

                if loop_samples_to_copy > 0:
                    loop_segment = original_sample[loop_start + loop_pos:loop_start + loop_pos + loop_samples_to_copy]
                    input_segment[samples_generated:samples_generated + loop_samples_to_copy] = loop_segment
                    samples_generated += loop_samples_to_copy
                    current_input_pos += loop_samples_to_copy

                    # If we've reached the end of the loop, wrap back to loop start
                    if current_input_pos >= loop_end:
                        current_input_pos = loop_start

            else:
                # Beyond loop end - shouldn't happen in sustain mode, but handle it
                remaining_samples = input_samples_needed - samples_generated
                available_samples = sample_length - current_input_pos
                samples_to_copy = min(remaining_samples, available_samples)

                if samples_to_copy > 0:
                    tail_segment = original_sample[current_input_pos:current_input_pos + samples_to_copy]
                    input_segment[samples_generated:samples_generated + samples_to_copy] = tail_segment
                    samples_generated += samples_to_copy
                    current_input_pos += samples_to_copy

                # If we can't get more samples, break
                break

        # Apply resampling for pitch bend and sample rate conversion
        output_segment = resample_segment(input_segment, output_samples)

        # Return the new input offset (for looping, we track where we are in the virtual extended sample)
        return output_segment, current_input_pos

    def handle_note_stealing(channel: int, velocity: float):
        if channel_notes_count[channel] < polyphony_limit:
            return None
            
        channel_notes = [(id, info) for id, info in active_notes.items() 
                        if info['channel'] == channel]
        
        if not channel_notes:
            return None
            
        # Try to steal releasing notes first
        releasing_notes = [(id, info) for id, info in channel_notes 
                         if info.get('is_releasing', False)]
        if releasing_notes:
            return min(releasing_notes, key=lambda x: x[1]['velocity'])[0]
            
        # Then try to steal quieter notes
        quiet_notes = [(id, info) for id, info in channel_notes 
                      if info['velocity'] < velocity]
        if quiet_notes:
            return min(quiet_notes, key=lambda x: x[1]['velocity'])[0]
            
        # Finally steal the oldest note
        return min(channel_notes, key=lambda x: x[1]['start_time'])[0]

    def apply_cc_effects(audio_segment: np.ndarray, channel: int) -> np.ndarray:
        """Apply CC effects (volume, pan, expression, modulation, filter) to audio segment"""
        if len(audio_segment) == 0:
            return audio_segment

        cc_state = channel_cc_state[channel]

        # Apply volume (CC 7 + CC 39 LSB) and expression (CC 11 + CC 43 LSB) - both affect amplitude
        # Combine MSB and LSB for 14-bit precision
        volume_14bit = (cc_state['volume'] << 7) + cc_state['volume_lsb']
        expression_14bit = (cc_state['expression'] << 7) + cc_state['expression_lsb']

        volume_factor = (volume_14bit / 16383.0)  # 14-bit max value
        expression_factor = (expression_14bit / 16383.0)
        amplitude_factor = volume_factor * expression_factor

        # Apply pan (CC 10) - affects left/right balance
        pan_value = cc_state['pan']  # 0=left, 64=center, 127=right
        if pan_value <= 64:
            # Pan towards left
            left_gain = 1.0
            right_gain = pan_value / 64.0
        else:
            # Pan towards right
            left_gain = (127 - pan_value) / 63.0
            right_gain = 1.0

        # Apply modulation (CC 1) - simple vibrato effect
        modulation_amount = cc_state['modulation'] / 127.0

        # Apply filter effects (CC 71 Resonance, CC 74 Cutoff)
        # Simple brightness/tone control based on cutoff frequency
        cutoff_factor = cc_state['cutoff'] / 127.0  # 0.0 = dark, 1.0 = bright
        resonance_factor = cc_state['resonance'] / 127.0  # 0.0 = no resonance, 1.0 = max resonance

        # Create output with proper shape
        output = audio_segment.copy()

        # Apply amplitude effects
        output *= amplitude_factor

        # Apply simple tone control (cutoff approximation)
        # Higher cutoff = brighter sound, lower cutoff = darker sound
        if cutoff_factor < 1.0:
            # Simple high-frequency rolloff for darker sound
            brightness_factor = 0.3 + (cutoff_factor * 0.7)  # Range from 0.3 to 1.0
            output *= brightness_factor

        # Apply panning
        if output.shape[1] >= 2:  # Stereo
            output[:, 0] *= left_gain   # Left channel
            output[:, 1] *= right_gain  # Right channel

        # Apply simple modulation (vibrato) if modulation > 0
        if modulation_amount > 0.01:
            vibrato_rate = 5.0  # Hz
            vibrato_depth = 0.02 * modulation_amount  # Max 2% pitch variation
            for i in range(len(output)):
                time_sec = i / sample_rate
                vibrato_factor = 1.0 + vibrato_depth * np.sin(2 * np.pi * vibrato_rate * time_sec)
                # Simple pitch modulation by slightly adjusting the sample timing
                # This is a simplified vibrato - more complex implementations could use proper pitch shifting
                if i > 0 and vibrato_factor != 1.0:
                    # Blend with previous sample for smooth vibrato
                    blend_factor = abs(vibrato_factor - 1.0) * 0.5
                    output[i] = output[i] * (1 - blend_factor) + output[i-1] * blend_factor

        return output

    for event in events:
        event_time_raw = get_current_time(event.time)
        
        # Apply quantization if enabled
        event_time = quantize_time(event_time_raw, processing_fps, fps_fluctuation)

        if event_time < chunk_start_time:
            # Still need to process pitch bends and CC events that might affect notes starting later
            if event.event_type == 'pitch_bend':
                 channel_pitch_bend[event.channel] = event.pitch_bend
            elif event.event_type == 'control_change':
                # Update CC state even for events before chunk start
                cc_number = event.cc_number
                cc_value = event.cc_value
                if cc_number == 1:  # Modulation
                    channel_cc_state[event.channel]['modulation'] = cc_value
                elif cc_number == 7:  # Volume
                    channel_cc_state[event.channel]['volume'] = cc_value
                elif cc_number == 10:  # Pan
                    channel_cc_state[event.channel]['pan'] = cc_value
                elif cc_number == 11:  # Expression
                    channel_cc_state[event.channel]['expression'] = cc_value
                elif cc_number == 39:  # Volume LSB
                    channel_cc_state[event.channel]['volume_lsb'] = cc_value
                elif cc_number == 43:  # Expression LSB
                    channel_cc_state[event.channel]['expression_lsb'] = cc_value
                elif cc_number == 71:  # Resonance
                    channel_cc_state[event.channel]['resonance'] = cc_value
                elif cc_number == 74:  # Cutoff
                    channel_cc_state[event.channel]['cutoff'] = cc_value
            continue # Skip rendering for events before chunk start
        if event_time >= chunk_end_time:
            break

        if event.event_type == 'pitch_bend':
            new_pitch_bend = event.pitch_bend
            
            for note_id, note_info in list(active_notes.items()):
                if note_info['channel'] == event.channel:
                    segment_start = note_info['segment_start']
                    segment_end = int((event_time - chunk_start_time) * sample_rate)

                    # Initialize variables in case no processing occurs
                    input_consumed = 0
                    new_input_offset = note_info['input_offset']  # Default to current offset

                    if segment_end > segment_start:
                        output_samples = segment_end - segment_start

                        # Apply pitch bending with proper SF2 looping
                        pitched_segment, new_input_offset = apply_pitch_bend_with_looping(
                            note_info['original_sample'],
                            note_info['input_offset'],
                            output_samples,
                            note_info['current_pitch_bend'],
                            note_info['original_sample_rate'],
                            sample_rate,
                            note_info.get('loop_info'),
                            note_info.get('is_releasing', False)
                        )

                        # Use the new input offset returned by the looping function
                        input_consumed = new_input_offset - note_info['input_offset']

                        actual_end = min(segment_end, len(output))
                        segment_length = min(len(pitched_segment), actual_end - segment_start)
                        if segment_length > 0:
                            # Apply CC effects to the segment
                            processed_segment = apply_cc_effects(
                                pitched_segment[:segment_length], note_info['channel']
                            )
                            output[segment_start:segment_start + segment_length] += \
                                processed_segment * note_info['velocity']

                    active_notes[note_id].update({
                        'segment_start': segment_end,
                        'input_offset': new_input_offset,
                        'current_pitch_bend': new_pitch_bend
                    })
            
            channel_pitch_bend[event.channel] = new_pitch_bend

        elif event.event_type == 'control_change':
            # Update CC state for the channel
            cc_number = event.cc_number
            cc_value = event.cc_value

            if cc_number == 1:  # Modulation
                channel_cc_state[event.channel]['modulation'] = cc_value
            elif cc_number == 7:  # Volume
                channel_cc_state[event.channel]['volume'] = cc_value
            elif cc_number == 10:  # Pan
                channel_cc_state[event.channel]['pan'] = cc_value
            elif cc_number == 11:  # Expression
                channel_cc_state[event.channel]['expression'] = cc_value
            elif cc_number == 39:  # Volume LSB
                channel_cc_state[event.channel]['volume_lsb'] = cc_value
            elif cc_number == 43:  # Expression LSB
                channel_cc_state[event.channel]['expression_lsb'] = cc_value
            elif cc_number == 71:  # Resonance
                channel_cc_state[event.channel]['resonance'] = cc_value
            elif cc_number == 74:  # Cutoff
                channel_cc_state[event.channel]['cutoff'] = cc_value
            elif cc_number == 120:  # All Sound Off
                # Stop all notes on this channel immediately
                notes_to_remove = [note_id for note_id, note_info in active_notes.items()
                                 if note_info['channel'] == event.channel]
                for note_id in notes_to_remove:
                    channel_notes_count[active_notes[note_id]['channel']] -= 1
                    del active_notes[note_id]
            elif cc_number == 123:  # All Notes Off
                # Stop all notes on this channel with release time
                for note_id, note_info in active_notes.items():
                    if note_info['channel'] == event.channel and not note_info.get('is_releasing', False):
                        active_notes[note_id]['is_releasing'] = True
                        active_notes[note_id]['release_start'] = event_time

        elif event.event_type == 'note_on':
            note_start = int((event_time - chunk_start_time) * sample_rate)
            note = event.param1
            velocity = scale_velocity(event.param2)
            channel = event.channel

            # Handle velocity 0 note-on as immediate note-off (MIDI standard)
            if velocity == 0:
                key = (event.channel, event.param1)
                if key in note_ids and note_ids[key]:
                    note_id = note_ids[key].pop(0)
                    if note_id in active_notes:
                        # Stop the note immediately - no release time
                        channel_notes_count[active_notes[note_id]['channel']] -= 1
                        del active_notes[note_id]
            elif velocity > 0 and (channel, note) in samples:
                # Handle note stealing if needed
                note_to_steal = handle_note_stealing(event.channel, velocity)
                if note_to_steal is not None:
                    stolen_note = active_notes[note_to_steal]
                    if not disable_release_fade:
                        # Apply quick fade-out when stealing notes (like older version)
                        quick_release = min(0.01, release_time / 2)
                        segment_start = stolen_note['segment_start']
                        note_end = int((event_time - chunk_start_time + quick_release) * sample_rate)

                        if note_end > segment_start:
                            output_samples = note_end - segment_start

                            # Apply pitch bending with proper SF2 looping for stolen note
                            pitched_segment, _ = apply_pitch_bend_with_looping(
                                stolen_note['original_sample'],
                                stolen_note['input_offset'],
                                output_samples,
                                stolen_note['current_pitch_bend'],
                                stolen_note['original_sample_rate'],
                                sample_rate,
                                stolen_note.get('loop_info'),
                                stolen_note.get('is_releasing', False)
                            )

                            if len(pitched_segment) > 0:
                                # Apply quick release envelope for note stealing using optimized function
                                processed_segment = apply_release_envelope_optimized(
                                    pitched_segment, len(pitched_segment), quick_release,
                                    sample_rate, stolen_note['velocity']
                                )
                                # Apply CC effects to the processed segment
                                cc_processed_segment = apply_cc_effects(processed_segment, stolen_note['channel'])
                                output[segment_start:segment_start + len(pitched_segment)] += cc_processed_segment

                    # When disable_release_fade is True, just delete the note immediately without processing
                    channel_notes_count[stolen_note['channel']] -= 1
                    del active_notes[note_to_steal]

                channel_notes_count[event.channel] += 1
                original_sample_data, original_sample_rate, loop_info = samples[(channel, note)]

                note_id = next_note_id
                next_note_id += 1

                active_notes[note_id] = {
                    'start': note_start,
                    'segment_start': note_start,
                    'original_sample': original_sample_data,
                    'original_sample_rate': original_sample_rate,  # Store the original sample rate
                    'velocity': velocity,
                    'start_time': event_time,
                    'channel': event.channel,
                    'note': note,
                    'current_pitch_bend': channel_pitch_bend[event.channel],
                    'input_offset': 0,  # Track position in original sample for proper resampling
                    'is_releasing': False,
                    'loop_info': loop_info  # Store loop information for proper SF2 sustain
                }

                key = (event.channel, note)
                if key not in note_ids:
                    note_ids[key] = []
                note_ids[key].append(note_id)

        elif event.event_type == 'note_off':
            key = (event.channel, event.param1)
            if key in note_ids and note_ids[key]:
                note_id = note_ids[key].pop(0)
                if note_id in active_notes:
                    note_info = active_notes[note_id]

                    # Check if this is a velocity 0 note-off (immediate stop) or param2 is 0
                    # In MIDI, note-off velocity can indicate how quickly to stop
                    immediate_stop = (event.param2 == 0)

                    if immediate_stop:
                        # Stop the note immediately - no release time
                        channel_notes_count[note_info['channel']] -= 1
                        del active_notes[note_id]
                        continue  # Skip the release processing below

                    note_info['is_releasing'] = True
                    channel_notes_count[note_info['channel']] -= 1

                    segment_start = note_info['segment_start']

                    # Calculate how much of the sample is left to play
                    remaining_sample_length = len(note_info['original_sample']) - note_info['input_offset']

                    # Both cases should use the user's release time setting for duration control
                    release_samples = int(release_time * sample_rate)

                    if disable_release_fade:
                        # When release fade is disabled, play for release_time duration without fade-out
                        note_end = int((event_time - chunk_start_time) * sample_rate) + release_samples
                    else:
                        # With release fade, play for release_time duration with fade-out
                        # Ensure minimum length for smooth fade
                        min_release_samples = int(0.01 * sample_rate)  # Minimum 10ms for fade
                        release_samples = max(release_samples, min_release_samples)
                        note_end = int((event_time - chunk_start_time) * sample_rate) + release_samples

                    actual_end = min(note_end, len(output))

                    if actual_end > segment_start:
                        output_samples = actual_end - segment_start

                        # Apply pitch bending with proper SF2 looping
                        pitched_segment, new_input_offset = apply_pitch_bend_with_looping(
                            note_info['original_sample'],
                            note_info['input_offset'],
                            output_samples,
                            note_info['current_pitch_bend'],
                            note_info['original_sample_rate'],
                            sample_rate,
                            note_info.get('loop_info'),
                            note_info.get('is_releasing', False)
                        )

                        segment_length = min(len(pitched_segment), actual_end - segment_start)
                        if segment_length > 0:
                            if disable_release_fade:
                                # No fade-out, just play the remaining audio
                                cc_processed_segment = apply_cc_effects(
                                    pitched_segment[:segment_length], note_info['channel']
                                )
                                output[segment_start:segment_start + segment_length] += \
                                    cc_processed_segment * note_info['velocity']
                            else:
                                # Apply release envelope using optimized function
                                processed_segment = apply_release_envelope_optimized(
                                    pitched_segment, segment_length, release_time,
                                    sample_rate, note_info['velocity']
                                )
                                # Apply CC effects to the processed segment
                                cc_processed_segment = apply_cc_effects(processed_segment, note_info['channel'])
                                output[segment_start:segment_start + segment_length] += cc_processed_segment
                    
                    del active_notes[note_id]

    # Handle remaining active notes at chunk end
    for note_id, note_info in list(active_notes.items()):
        segment_start = note_info['segment_start']

        # Calculate how much of the sample is left to play
        remaining_sample_length = len(note_info['original_sample']) - note_info['input_offset']

        if disable_release_fade:
            # When release fade is disabled, play the full remaining sample or until chunk end
            max_output_samples = len(output) - segment_start
            # Allow the full sample to play, not just a short release time
            output_samples = min(remaining_sample_length, max_output_samples)
        else:
            # With release fade, limit to release time but ensure we don't cut off too early
            release_samples = int(release_time * sample_rate)
            max_output_samples = len(output) - segment_start
            output_samples = min(remaining_sample_length, max_output_samples, release_samples)

        actual_end = segment_start + output_samples

        if actual_end > segment_start and output_samples > 0:
            # Apply pitch bending with proper SF2 looping
            pitched_segment, new_input_offset = apply_pitch_bend_with_looping(
                note_info['original_sample'],
                note_info['input_offset'],
                output_samples,
                note_info['current_pitch_bend'],
                note_info['original_sample_rate'],
                sample_rate,
                note_info.get('loop_info'),
                note_info.get('is_releasing', False)
            )

            segment_length = min(len(pitched_segment), actual_end - segment_start)
            if segment_length > 0:
                if disable_release_fade:
                    # No fade-out, just play the remaining audio
                    cc_processed_segment = apply_cc_effects(
                        pitched_segment[:segment_length], note_info['channel']
                    )
                    output[segment_start:segment_start + segment_length] += \
                        cc_processed_segment * note_info['velocity']
                else:
                    # Apply release envelope using optimized function
                    processed_segment = apply_release_envelope_optimized(
                        pitched_segment, segment_length, release_time,
                        sample_rate, note_info['velocity']
                    )
                    # Apply CC effects to the processed segment
                    cc_processed_segment = apply_cc_effects(processed_segment, note_info['channel'])
                    output[segment_start:segment_start + segment_length] += cc_processed_segment

    return output

# --- Sound Effects ---

@njit(fastmath=True)
def apply_simple_reverb(audio, sample_rate, amount):
    """Applies a simple multi-tap feedback reverb effect."""
    if amount <= 0 or audio.ndim != 2: # Ensure stereo
        return audio

    mix = amount / 100.0
    wet_signal = np.zeros_like(audio)
    
    # Define multiple delay taps (lengths in seconds, chosen somewhat arbitrarily)
    # Use slightly different times for L/R channels for stereo effect
    delay_times_l = np.array([0.0297, 0.0371, 0.0411, 0.0437])
    delay_times_r = np.array([0.0313, 0.0389, 0.0423, 0.0451])
    feedback = 0.25 * mix # Lower feedback for multiple taps
    decay = 0.4 * mix    # Overall decay factor

    delay_samples_l = np.array([(int(t * sample_rate)) for t in delay_times_l])
    delay_samples_r = np.array([(int(t * sample_rate)) for t in delay_times_r])
    
    max_delay_l = np.max(delay_samples_l) if len(delay_samples_l) > 0 else 0
    max_delay_r = np.max(delay_samples_r) if len(delay_samples_r) > 0 else 0

    # Process Left Channel
    if max_delay_l > 0:
        # Using a simple circular buffer approach for feedback
        buffer_l = np.zeros(max_delay_l + 1) 
        write_pos_l = 0
        for i in range(len(audio)):
            output_sample = 0.0
            for j in range(len(delay_samples_l)):
                 read_pos = (write_pos_l - delay_samples_l[j] + len(buffer_l)) % len(buffer_l)
                 output_sample += buffer_l[read_pos]
            
            output_sample *= (decay / len(delay_samples_l)) # Average contribution

            # Feedback loop
            current_input = audio[i, 0] + output_sample * feedback
            buffer_l[write_pos_l] = current_input
            wet_signal[i, 0] = output_sample
            write_pos_l = (write_pos_l + 1) % len(buffer_l)


    # Process Right Channel (similar logic, different delays)
    if max_delay_r > 0:
        buffer_r = np.zeros(max_delay_r + 1)
        write_pos_r = 0
        for i in range(len(audio)):
            output_sample = 0.0
            for j in range(len(delay_samples_r)):
                 read_pos = (write_pos_r - delay_samples_r[j] + len(buffer_r)) % len(buffer_r)
                 output_sample += buffer_r[read_pos]

            output_sample *= (decay / len(delay_samples_r))

            current_input = audio[i, 1] + output_sample * feedback
            buffer_r[write_pos_r] = current_input
            wet_signal[i, 1] = output_sample
            write_pos_r = (write_pos_r + 1) % len(buffer_r)


    # Clipping to prevent excessive levels
    for i in range(len(wet_signal)):
        for j in range(2):
            if wet_signal[i, j] > 0.9:
                wet_signal[i, j] = 0.9
            elif wet_signal[i, j] < -0.9:
                wet_signal[i, j] = -0.9

    # Mix dry and wet signals
    result = np.zeros_like(audio)
    for i in range(len(audio)):
        for j in range(2):
            result[i, j] = audio[i, j] * (1.0 - mix) + wet_signal[i, j] * mix
            if result[i, j] > 1.0:
                result[i, j] = 1.0
            elif result[i, j] < -1.0:
                result[i, j] = -1.0

    return result


# Replace the chorus function with this Numba-accelerated version
@njit(fastmath=True)
def apply_simple_chorus(audio, sample_rate, amount):
    """Applies a simple stereo chorus effect."""
    if amount <= 0 or audio.ndim != 2: # Ensure stereo
        return audio

    mix = amount / 100.0
    rate_hz = 0.6     # LFO rate
    depth = 0.002 * sample_rate * mix # Modulation depth in samples, scaled by mix

    wet_signal = np.zeros_like(audio)
    num_samples = len(audio)
    
    # Precompute LFO signals (sine for L, cosine for R for simple stereo phase difference)
    lfo_l = np.zeros(num_samples)
    lfo_r = np.zeros(num_samples)
    
    for i in range(num_samples):
        t = i / sample_rate
        lfo_l[i] = np.sin(2 * np.pi * rate_hz * t) * depth
        lfo_r[i] = np.cos(2 * np.pi * rate_hz * t) * depth

    # Base delay + modulation
    base_delay_samples = 0.005 * sample_rate # 5ms base delay
    
    # Left Channel
    for i in range(num_samples):
        delay_samples = base_delay_samples + lfo_l[i]
        read_index = i - delay_samples
        
        if read_index < 0:
            wet_signal[i, 0] = 0.0  # Simple boundary handling
        else:
            idx_floor = int(read_index)
            idx_ceil = min(num_samples - 1, idx_floor + 1)
            frac = read_index - idx_floor
            
            # Linear interpolation
            if idx_floor >= 0 and idx_floor < num_samples:
                sample_floor = audio[idx_floor, 0]
            else:
                sample_floor = 0.0
                
            if idx_ceil >= 0 and idx_ceil < num_samples:
                sample_ceil = audio[idx_ceil, 0]
            else:
                sample_ceil = 0.0
                
            wet_signal[i, 0] = sample_floor * (1 - frac) + sample_ceil * frac
    
    # Right Channel
    for i in range(num_samples):
        delay_samples = base_delay_samples + lfo_r[i]
        read_index = i - delay_samples
        
        if read_index < 0:
            wet_signal[i, 1] = 0.0  # Simple boundary handling
        else:
            idx_floor = int(read_index)
            idx_ceil = min(num_samples - 1, idx_floor + 1)
            frac = read_index - idx_floor
            
            # Linear interpolation
            if idx_floor >= 0 and idx_floor < num_samples:
                sample_floor = audio[idx_floor, 1]
            else:
                sample_floor = 0.0
                
            if idx_ceil >= 0 and idx_ceil < num_samples:
                sample_ceil = audio[idx_ceil, 1]
            else:
                sample_ceil = 0.0
                
            wet_signal[i, 1] = sample_floor * (1 - frac) + sample_ceil * frac

    # Clipping
    for i in range(len(wet_signal)):
        for j in range(2):
            if wet_signal[i, j] > 0.9:
                wet_signal[i, j] = 0.9
            elif wet_signal[i, j] < -0.9:
                wet_signal[i, j] = -0.9
    
    # Mix dry and wet signals
    result = np.zeros_like(audio)
    for i in range(len(audio)):
        for j in range(2):
            result[i, j] = audio[i, j] * (1.0 - mix) + wet_signal[i, j] * mix
            if result[i, j] > 1.0:
                result[i, j] = 1.0
            elif result[i, j] < -1.0:
                result[i, j] = -1.0

    return result

# --- Normalization ---

def smooth_normalize(audio: np.ndarray, window_size: int, target_peak: float, target_rms: float,
                    attack: float, release: float, max_boost_db: float, max_cut_db: float,
                    previous_level: float = None) -> Tuple[np.ndarray, float]:
    # Convert dB values to linear scale
    target_peak_linear = 10 ** (target_peak / 20)
    target_rms_linear = 10 ** (target_rms / 20)
    max_boost_linear = 10 ** (max_boost_db / 20)
    max_cut_linear = 10 ** (max_cut_db / 20)
    
    # Downsampling for faster processing
    downsample_factor = max(1, min(20, window_size // 50))
    downsampled_audio = audio[::downsample_factor]
    
    # Calculate RMS and peak levels
    if downsampled_audio.ndim > 1:
        rms = np.sqrt(np.mean(downsampled_audio**2, axis=1))
        peak = np.max(np.abs(downsampled_audio), axis=1)
    else:
        rms = np.sqrt(np.mean(downsampled_audio**2))
        peak = np.max(np.abs(downsampled_audio))
    # Combine RMS and peak information
    level = np.maximum(rms / target_rms_linear, peak / target_peak_linear)
    
    if previous_level is not None:
        level = np.maximum(level[0], previous_level)
    
    # Apply attack and release
    attack_coeff = np.exp(-1 / (attack * window_size / downsample_factor))
    release_coeff = np.exp(-1 / (release * window_size / downsample_factor))
    
    forward_smooth = signal.lfilter([1 - attack_coeff], [1, -attack_coeff], level)
    backward_smooth = signal.lfilter([1 - release_coeff], [1, -release_coeff], level[::-1])[::-1]
    
    smoothed_level = np.maximum(forward_smooth, backward_smooth)
    smoothed_level = np.maximum(smoothed_level, 1e-8)
    
    # Calculate scaling factors with limits
    scaling_factors = np.clip(1 / smoothed_level, max_cut_linear, max_boost_linear)
    
    # Upsample scaling factors
    smoothed_factors = np.interp(np.arange(len(audio)),
                                np.linspace(0, len(audio) - 1, len(scaling_factors)),
                                scaling_factors)
    
    if audio.ndim > 1:
        smoothed_factors = smoothed_factors.reshape(-1, 1)
    
    normalized_audio = audio * smoothed_factors
    
    return normalized_audio, smoothed_level[-1]
    
def process_chunk(chunk_data: Tuple[int, float, float, List[bytes], Dict, int, List[Tuple[int, int]], int, float, float,
                                  float, float, float, float, float, float, float, Queue, bool, int, float, float, float]) -> Tuple[np.ndarray, float]: # Added reverb/chorus amounts
    (chunk_index, chunk_start, chunk_end, tracks, samples, ticks_per_beat, tempo_changes, sample_rate,
     polyphony_limit, release_time, target_peak, target_rms, attack, release, max_boost_db, max_cut_db,
     previous_level, message_queue, disable_release_fade,
     processing_fps, fps_fluctuation, reverb_amount, chorus_amount) = chunk_data # Added reverb/chorus unpacking
    
    chunk_duration = chunk_end - chunk_start
    output = np.zeros((int(chunk_duration * sample_rate), 2), dtype=np.float32)
    
    
    for i, track in enumerate(tracks):
        # First processing track should add to log
        if i == 0:
            message_queue.put(f"Processing chunk {chunk_index + 1}, track {i + 1}/{len(tracks)} [{0:>3.1f}%]")
    
        # Process track
        # When disable_release_fade is False, samples are pre-processed with fade-out
        track_audio = process_track_chunk(
            track, samples, ticks_per_beat, tempo_changes, sample_rate,
            chunk_start, chunk_end, polyphony_limit, release_time,
            processing_fps, fps_fluctuation, # Added
            disable_release_fade
        )
        output += track_audio
    
        # Update progress after processing each track
        percentage = ((i + 1) / len(tracks)) * 100
        message_queue.put(f"Processing chunk {chunk_index + 1}, track {i + 1}/{len(tracks)} [{percentage:>3.1f}%]")
    
    # Apply effects before normalization
    if reverb_amount > 0:
        output = apply_simple_reverb(output, sample_rate, reverb_amount)
    if chorus_amount > 0:
        output = apply_simple_chorus(output, sample_rate, chorus_amount)

    window_duration = 0.01
    window_size = int(window_duration * sample_rate)
    
    normalized_output, new_level = smooth_normalize(
        output, window_size, target_peak, target_rms,
        attack, release, max_boost_db, max_cut_db,
        previous_level
    )
    
    return normalized_output, new_level

def render_midi_to_audio(midi_path: str, samples: Dict[Tuple[int, int], Tuple[np.ndarray, int, dict]], output_path: str,
                        chunk_duration: float, polyphony_limit: int, release_time: float,
                        target_peak: float, target_rms: float, attack: float, release: float,
                        max_boost_db: float, max_cut_db: float, message_queue: Queue,
                        disable_release_fade: bool = False,
                        processing_fps: int = 0, fps_fluctuation: float = 0.0,
                        reverb_amount: float = 0.0, chorus_amount: float = 0.0): # Added effects params

    message_queue.put("Starting MIDI rendering process...")

    # Initialize pre-computed envelopes for performance optimization
    if not disable_release_fade:
        message_queue.put("Pre-computing release envelopes for optimization...")
        sample_rate = next(iter(samples.values()))[1]  # Get sample rate from first sample
        _initialize_precomputed_envelopes(release_time, sample_rate)
    
    ticks_per_beat, num_tracks, tracks, tempo_changes = parse_midi_file(
        midi_path, 
        lambda desc, cur, tot: message_queue.put(f"{desc}: {cur}/{tot}")
    )

    target_sample_rate = 44100  # Force output sample rate
    message_queue.put(f"Target sample rate set to: {target_sample_rate} Hz")

    # Skip resampling - handle different sample rates during playback for much better performance
    message_queue.put(f"Skipping sample resampling for performance - will handle rate conversion during playback")

    # Just ensure all samples are float32 and stereo
    processed_samples = {}
    for (channel, note), (sample_data, original_rate, loop_info) in samples.items():
        # Ensure output is float32
        if sample_data.dtype != np.float32:
            sample_data = sample_data.astype(np.float32)

        # Ensure stereo if needed
        if sample_data.ndim == 1:
            sample_data = np.column_stack((sample_data, sample_data))

        # Keep the original loop info (don't overwrite it!)
        processed_samples[(channel, note)] = (sample_data, original_rate, loop_info)

    message_queue.put(f"Processed {len(processed_samples)} samples (no resampling)")

    if not processed_samples:
        message_queue.put("Error: No usable samples after processing.")
        return # Stop processing if no samples are left

    # Use the processed samples dictionary from now on
    samples = processed_samples
    sample_rate = target_sample_rate # Use the target rate for output calculations

    max_time = max(max(event.time for event in parse_midi_events(track)) for track in tracks)
    total_seconds = sum(tick2second(tempo_changes[i+1][0] - tempo_changes[i][0], 
                                  ticks_per_beat, tempo_changes[i][1])
                       for i in range(len(tempo_changes) - 1))
    total_seconds += tick2second(max_time - tempo_changes[-1][0], 
                               ticks_per_beat, tempo_changes[-1][1])
    
    message_queue.put(f"Sample rate: {sample_rate} Hz")
    message_queue.put(f"Chunk duration: {chunk_duration} seconds")
    message_queue.put(f"Number of tracks: {num_tracks}")
    message_queue.put(f"PPQ: {ticks_per_beat}")
    message_queue.put(f"MIDI duration: ~{int(total_seconds)} seconds "
                     f"({int(total_seconds/60)}:{int(total_seconds%60):02d})")
    
    num_chunks = int(np.ceil(total_seconds / chunk_duration))
    message_queue.put(f"Number of chunks: {num_chunks}")
    
    with sf.SoundFile(output_path, mode='w', samplerate=sample_rate, channels=2) as output_file:
        previous_level = None
        
        for chunk_index in range(num_chunks):
            chunk_start = chunk_index * chunk_duration
            chunk_end = min((chunk_index + 1) * chunk_duration, total_seconds)
            
            message_queue.put(f"Processing chunk {chunk_index + 1}/{num_chunks}")
            
            chunk_data = (
                chunk_index, chunk_start, chunk_end, tracks, samples, ticks_per_beat,
                tempo_changes, sample_rate, polyphony_limit, release_time,
                target_peak, target_rms, attack, release, max_boost_db, max_cut_db,
                previous_level, message_queue, disable_release_fade,
                processing_fps, fps_fluctuation, # Added
                reverb_amount, chorus_amount # Added
            )
            
            current_chunk, new_level = process_chunk(chunk_data)
            previous_level = new_level
            
            output_file.write(current_chunk)
            
    message_queue.put(f"Rendered audio saved to {output_path}")

if __name__ == "__main__":
    multiprocessing.freeze_support()
    gui = MIDIRendererGUI()
    gui.run()