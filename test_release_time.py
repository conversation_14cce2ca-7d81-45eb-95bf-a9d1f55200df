#!/usr/bin/env python3
"""
Test release time functionality.
"""

import os
import sys
import numpy as np
from hexsf2newparser import SF2New<PERSON>ars<PERSON>

def test_release_time(sf2_path: str):
    """Test that release time is being applied correctly."""
    print(f"Testing release time with: {sf2_path}")
    print("=" * 60)
    
    try:
        # Initialize and parse the SF2 file
        parser = SF2NewParser(sf2_path)
        parser.parse()
        parser.load_sample_data()
        
        print(f"✓ Loaded SF2 with {len(parser.sample_headers)} samples")
        
        # Get a sample from Grand Piano
        sample_map = parser.get_samples_for_preset(0)  # Grand Piano
        
        if 60 not in sample_map:  # Middle C
            print("No sample mapping found for middle C")
            return False
        
        sample_id, generators = sample_map[60]
        header = parser.sample_headers[sample_id]
        
        print(f"Testing sample {sample_id}: '{header.name}'")
        print(f"Sample rate: {header.sample_rate} Hz")
        print(f"Original sample length: {header.end - header.start} samples")
        
        # Test both methods
        print(f"\nTesting sample extraction methods:")
        
        # Test get_sample (original method)
        original_sample = parser.get_sample(sample_id)
        if original_sample is not None:
            duration = len(original_sample) / header.sample_rate
            print(f"✓ get_sample(): {len(original_sample)} samples ({duration:.3f}s)")
        else:
            print(f"✗ get_sample() failed")
            return False
        
        # Test get_stereo_sample (new method)
        stereo_sample = parser.get_stereo_sample(sample_id)
        if stereo_sample is not None:
            duration = len(stereo_sample) / header.sample_rate
            print(f"✓ get_stereo_sample(): {len(stereo_sample)} samples ({duration:.3f}s)")
        else:
            print(f"✗ get_stereo_sample() failed")
            return False
        
        # Test get_raw_sample_data (internal method)
        raw_sample = parser.get_raw_sample_data(sample_id)
        if raw_sample is not None:
            duration = len(raw_sample) / header.sample_rate
            print(f"✓ get_raw_sample_data(): {len(raw_sample)} samples ({duration:.3f}s)")
        else:
            print(f"✗ get_raw_sample_data() failed")
            return False
        
        # Compare lengths
        print(f"\nLength comparison:")
        print(f"  Original sample length: {header.end - header.start} samples")
        print(f"  get_sample() length: {len(original_sample)} samples")
        print(f"  get_stereo_sample() length: {len(stereo_sample)} samples")
        print(f"  get_raw_sample_data() length: {len(raw_sample)} samples")
        
        # Check if they're the same length (they should be)
        if len(original_sample) == len(raw_sample) == len(stereo_sample):
            print(f"✓ All methods return same length - good!")
        else:
            print(f"⚠ Methods return different lengths - this might indicate an issue")
        
        # Test loop information
        loop_info = parser.get_sample_loop_info(sample_id)
        if loop_info:
            print(f"\nLoop information:")
            print(f"  Has loop: {loop_info['has_loop']}")
            if loop_info['has_loop']:
                print(f"  Loop start: {loop_info['loop_start']} samples")
                print(f"  Loop end: {loop_info['loop_end']} samples")
                print(f"  Loop length: {loop_info['loop_length']} samples")
        
        print(f"\n" + "=" * 60)
        print("Release time test completed!")
        
        # The actual release time is applied in the main MIDI processing, not in sample extraction
        # The sample extraction should return the full sample, and release time is applied during playback
        print(f"\nNote: Release time (0.050s) is applied during MIDI playback, not during sample extraction.")
        print(f"The sample extraction methods should return the full sample data.")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during test: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """Main test function."""
    # Try to find a soundfont
    test_paths = [
        "arachno.sf2",
        "Arachno.sf2", 
        "arachno-soundfont.sf2",
        "Arachno SoundFont - Version 1.0.sf2"
    ]
    
    sf2_path = None
    for path in test_paths:
        if os.path.exists(path):
            sf2_path = path
            break
    
    if not sf2_path:
        print("Soundfont not found. Please provide a path:")
        sf2_path = input("Enter SF2 file path: ").strip()
        if not sf2_path or not os.path.exists(sf2_path):
            print("Invalid or missing soundfont file.")
            return False
    
    return test_release_time(sf2_path)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
