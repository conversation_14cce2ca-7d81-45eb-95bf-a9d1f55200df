#!/usr/bin/env python3
"""
Test script to verify that velocity 0 events stop notes immediately.
Creates a MIDI file with velocity 0 note-on and note-off events to test immediate stopping.
"""

import mido
import os
import sys

def create_velocity_zero_test_midi():
    """Create a MIDI file to test velocity 0 immediate stopping"""
    
    # Create MIDI file
    mid = mido.MidiFile()
    track = mido.MidiTrack()
    mid.tracks.append(track)
    
    # Set tempo (120 BPM)
    track.append(mido.MetaMessage('set_tempo', tempo=500000, time=0))
    
    # Program change to piano (program 0)
    track.append(mido.Message('program_change', channel=0, program=0, time=0))
    
    # Test 1: Normal note with velocity 0 note-on to stop it
    print("Creating test MIDI with velocity 0 events...")
    
    # Note on C4 (note 60) with normal velocity
    track.append(mido.Message('note_on', channel=0, note=60, velocity=100, time=0))
    
    # After 0.5 seconds, send velocity 0 note-on (should stop immediately)
    track.append(mido.Message('note_on', channel=0, note=60, velocity=0, time=int(0.5 * 480)))
    
    # Test 2: Another note with velocity 0 note-off
    # Note on D4 (note 62) with normal velocity after 1 second
    track.append(mido.Message('note_on', channel=0, note=62, velocity=100, time=int(0.5 * 480)))
    
    # After 0.5 seconds, send note-off with velocity 0 (should stop immediately)
    track.append(mido.Message('note_off', channel=0, note=62, velocity=0, time=int(0.5 * 480)))
    
    # Test 3: Normal note with normal note-off (for comparison)
    # Note on E4 (note 64) with normal velocity after 1 second
    track.append(mido.Message('note_on', channel=0, note=64, velocity=100, time=int(0.5 * 480)))
    
    # After 0.5 seconds, send normal note-off (should use release time)
    track.append(mido.Message('note_off', channel=0, note=64, velocity=64, time=int(0.5 * 480)))
    
    # Save the file
    filename = 'test_velocity_zero.mid'
    mid.save(filename)
    print(f"Created {filename}")
    
    return filename

def analyze_midi_events(filename):
    """Analyze the MIDI events in the test file"""
    print(f"\nAnalyzing MIDI events in {filename}:")
    print("-" * 50)
    
    mid = mido.MidiFile(filename)
    current_time = 0
    
    for i, track in enumerate(mid.tracks):
        print(f"Track {i}:")
        for msg in track:
            current_time += msg.time
            if msg.type in ['note_on', 'note_off']:
                time_sec = mido.tick2second(current_time, mid.ticks_per_beat, 500000)  # 120 BPM
                print(f"  {time_sec:6.3f}s: {msg.type:8} ch={msg.channel} note={msg.note:3d} vel={msg.velocity:3d}")
                
                if msg.type == 'note_on' and msg.velocity == 0:
                    print(f"           ^^^ VELOCITY 0 NOTE-ON (should stop immediately)")
                elif msg.type == 'note_off' and msg.velocity == 0:
                    print(f"           ^^^ VELOCITY 0 NOTE-OFF (should stop immediately)")
            elif msg.type == 'program_change':
                time_sec = mido.tick2second(current_time, mid.ticks_per_beat, 500000)
                print(f"  {time_sec:6.3f}s: program_change ch={msg.channel} program={msg.program}")

def test_immediate_stopping_logic():
    """Test the logic for immediate stopping"""
    print("\nTesting immediate stopping logic:")
    print("-" * 40)
    
    # Simulate the conditions that should trigger immediate stopping
    test_cases = [
        ("Velocity 0 note-on", "note_on", 0),
        ("Velocity 0 note-off", "note_off", 0),
        ("Normal note-off", "note_off", 64),
        ("High velocity note-off", "note_off", 127),
    ]
    
    for description, event_type, velocity in test_cases:
        if event_type == "note_on" and velocity == 0:
            should_stop = True
            reason = "velocity 0 note-on treated as immediate note-off"
        elif event_type == "note_off" and velocity == 0:
            should_stop = True
            reason = "velocity 0 note-off means immediate stop"
        else:
            should_stop = False
            reason = "normal release time applies"
        
        status = "✓ IMMEDIATE STOP" if should_stop else "○ Normal release"
        print(f"{description:20} | {status:20} | {reason}")

def main():
    print("VELOCITY 0 IMMEDIATE STOP TEST")
    print("=" * 50)
    
    # Create test MIDI file
    filename = create_velocity_zero_test_midi()
    
    # Analyze the events
    analyze_midi_events(filename)
    
    # Test the logic
    test_immediate_stopping_logic()
    
    print("\n" + "=" * 50)
    print("EXPECTED BEHAVIOR:")
    print("- Note 60 (C4): Starts at 0.0s, stops immediately at 0.5s (velocity 0 note-on)")
    print("- Note 62 (D4): Starts at 1.0s, stops immediately at 1.5s (velocity 0 note-off)")
    print("- Note 64 (E4): Starts at 2.0s, stops with release time at 2.5s (normal note-off)")
    print("\nTo test this, process the MIDI file and listen for immediate vs. gradual stopping.")
    print(f"Test file created: {filename}")

if __name__ == "__main__":
    main()
