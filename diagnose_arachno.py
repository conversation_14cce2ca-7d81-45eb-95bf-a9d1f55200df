#!/usr/bin/env python3
"""
Diagnostic script specifically for Arachno soundfont issues.
"""

import os
import sys
import numpy as np
from hexsf2newparser import SF2NewParser

def diagnose_arachno_soundfont(sf2_path: str):
    """Diagnose issues with Arachno soundfont."""
    print(f"Diagnosing Arachno soundfont: {sf2_path}")
    print("=" * 80)
    
    try:
        # Initialize and parse the SF2 file
        parser = SF2NewParser(sf2_path)
        parser.parse()
        
        print(f"✓ Successfully parsed SF2 file")
        print(f"  - Found {len(parser.presets)} presets")
        print(f"  - Found {len(parser.sample_headers)} samples")
        print()
        
        # Load sample data
        parser.load_sample_data()
        
        # Find Grand Piano preset
        piano_preset_idx = None
        for i, preset in enumerate(parser.presets):
            preset_name = preset.name.lower()
            if 'grand' in preset_name and 'piano' in preset_name:
                piano_preset_idx = i
                print(f"Found Grand Piano preset {i}: '{preset.name}'")
                break
        
        if piano_preset_idx is None:
            print("No Grand Piano preset found. Looking for any piano preset...")
            for i, preset in enumerate(parser.presets):
                preset_name = preset.name.lower()
                if 'piano' in preset_name:
                    piano_preset_idx = i
                    print(f"Found Piano preset {i}: '{preset.name}'")
                    break
        
        if piano_preset_idx is None:
            print("No piano preset found. Using preset 0.")
            piano_preset_idx = 0
        
        # Analyze the piano preset
        print(f"\nAnalyzing preset {piano_preset_idx}: '{parser.presets[piano_preset_idx].name}'")
        print("-" * 60)
        
        sample_map = parser.get_samples_for_preset(piano_preset_idx)
        if not sample_map:
            print("Error: No sample mappings found for piano preset")
            return False
        
        print(f"Found {len(sample_map)} note mappings")
        
        # Test specific notes that commonly have issues
        test_notes = [21, 36, 48, 60, 72, 84, 96, 108]  # A0, C2, C3, C4, C5, C6, C7, C8
        
        print(f"\nTesting specific notes:")
        print("-" * 40)
        
        for note in test_notes:
            if note in sample_map:
                sample_id, generators = sample_map[note]
                header = parser.sample_headers[sample_id]
                
                print(f"\nNote {note} (MIDI):")
                print(f"  Sample ID: {sample_id}")
                print(f"  Sample name: '{header.name}'")
                print(f"  Sample type: {header.sample_type} ({parser._get_sample_type_string(header.sample_type)})")
                print(f"  Sample rate: {header.sample_rate} Hz")
                print(f"  Original pitch: {header.original_pitch}")
                print(f"  Sample range: {header.start} - {header.end} ({header.end - header.start} samples)")
                print(f"  Loop range: {header.loop_start} - {header.loop_end}")
                print(f"  Sample link: {header.sample_link}")
                
                # Test sample extraction
                try:
                    sample_data = parser.get_stereo_sample(sample_id)
                    if sample_data is not None:
                        duration = len(sample_data) / header.sample_rate
                        max_amp = np.max(np.abs(sample_data))
                        print(f"  ✓ Extracted: {len(sample_data)} samples ({duration:.3f}s)")
                        print(f"  ✓ Max amplitude: {max_amp:.6f}")
                        print(f"  ✓ Shape: {sample_data.shape}")
                        
                        # Check for stereo balance issues
                        if sample_data.shape[1] == 2:
                            left_max = np.max(np.abs(sample_data[:, 0]))
                            right_max = np.max(np.abs(sample_data[:, 1]))
                            balance_ratio = right_max / left_max if left_max > 0 else float('inf')
                            print(f"  ✓ Stereo balance: L={left_max:.6f}, R={right_max:.6f}, ratio={balance_ratio:.2f}")
                            
                            if balance_ratio > 10 or balance_ratio < 0.1:
                                print(f"  ⚠ WARNING: Severe stereo imbalance detected!")
                            
                            # Check if channels are completely different (might indicate wrong linking)
                            correlation = np.corrcoef(sample_data[:, 0], sample_data[:, 1])[0, 1]
                            if not np.isnan(correlation):
                                print(f"  ✓ Channel correlation: {correlation:.3f}")
                                if correlation < 0.1:
                                    print(f"  ⚠ WARNING: Very low channel correlation - might be wrong stereo linking!")
                        
                        # Check for very short samples
                        if duration < 0.1:
                            print(f"  ⚠ WARNING: Very short sample duration!")
                        
                        # Check for very quiet samples
                        if max_amp < 0.01:
                            print(f"  ⚠ WARNING: Very quiet sample!")
                            
                    else:
                        print(f"  ✗ Failed to extract sample")
                        
                except Exception as e:
                    print(f"  ✗ Error extracting sample: {e}")
            else:
                print(f"\nNote {note}: No mapping found")
        
        # Check for common stereo linking issues
        print(f"\n\nStereo Linking Analysis:")
        print("-" * 40)
        
        left_samples = []
        right_samples = []
        mono_samples = []
        
        for i, header in enumerate(parser.sample_headers):
            if header.sample_type == 4:  # Left channel
                left_samples.append(i)
            elif header.sample_type == 2:  # Right channel
                right_samples.append(i)
            elif header.sample_type == 1:  # Mono
                mono_samples.append(i)
        
        print(f"Found {len(left_samples)} left channel samples")
        print(f"Found {len(right_samples)} right channel samples")
        print(f"Found {len(mono_samples)} mono samples")
        
        # Check for broken stereo links
        broken_links = 0
        for left_id in left_samples[:10]:  # Check first 10 left samples
            header = parser.sample_headers[left_id]
            right_id = header.sample_link
            
            if right_id >= len(parser.sample_headers):
                print(f"  ✗ Left sample {left_id} links to invalid right sample {right_id}")
                broken_links += 1
            else:
                right_header = parser.sample_headers[right_id]
                if right_header.sample_type != 2:
                    print(f"  ✗ Left sample {left_id} links to non-right sample {right_id} (type {right_header.sample_type})")
                    broken_links += 1
                elif right_header.sample_link != left_id:
                    print(f"  ✗ Stereo link mismatch: {left_id} -> {right_id}, but {right_id} -> {right_header.sample_link}")
                    broken_links += 1
        
        if broken_links == 0:
            print("✓ No broken stereo links found in sample")
        else:
            print(f"⚠ Found {broken_links} broken stereo links")
        
        print(f"\n" + "=" * 80)
        print("Diagnosis completed!")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during diagnosis: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """Main diagnostic function."""
    print("Arachno Soundfont Diagnostic Tool")
    print("=" * 80)
    
    # Try to find Arachno soundfont
    test_paths = [
        "arachno.sf2",
        "Arachno.sf2",
        "arachno-soundfont.sf2",
        "Arachno SoundFont - Version 1.0.sf2"
    ]
    
    sf2_path = None
    for path in test_paths:
        if os.path.exists(path):
            sf2_path = path
            break
    
    if not sf2_path:
        print("Arachno soundfont not found. Please provide a path:")
        sf2_path = input("Enter SF2 file path: ").strip()
        if not sf2_path or not os.path.exists(sf2_path):
            print("Invalid or missing soundfont file.")
            return False
    
    return diagnose_arachno_soundfont(sf2_path)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
