#!/usr/bin/env python3
"""
Test that release time is properly applied in both fade and no-fade modes.
"""

import os
import sys
import json
import time

def test_release_time_behavior():
    """Test that release time controls duration in both modes."""
    print("Testing Release Time Behavior")
    print("=" * 60)
    
    # Read current settings
    settings_file = "hexsyn_settings.json"
    if not os.path.exists(settings_file):
        print(f"Settings file {settings_file} not found!")
        return False
    
    with open(settings_file, 'r') as f:
        settings = json.load(f)
    
    release_time = settings.get('release_time', 0.05)
    disable_release_fade = settings.get('disable_release_fade', False)
    
    print(f"Current Settings:")
    print(f"  Release Time: {release_time:.3f} seconds")
    print(f"  Disable Release Fade: {disable_release_fade}")
    print()
    
    # Test both modes
    test_modes = [
        {"disable_release_fade": False, "description": "WITH fade-out"},
        {"disable_release_fade": True, "description": "WITHOUT fade-out (hard cut)"}
    ]
    
    for mode in test_modes:
        print(f"Testing mode: {mode['description']}")
        print("-" * 40)
        
        # Update settings for this test
        settings['disable_release_fade'] = mode['disable_release_fade']
        
        with open(settings_file, 'w') as f:
            json.dump(settings, f, indent=4)
        
        print(f"✓ Set disable_release_fade = {mode['disable_release_fade']}")
        
        # Calculate expected behavior
        expected_duration_ms = release_time * 1000
        
        if mode['disable_release_fade']:
            print(f"✓ Expected: Notes play for {expected_duration_ms:.1f}ms then CUT OFF (no fade)")
        else:
            print(f"✓ Expected: Notes play for {expected_duration_ms:.1f}ms with FADE OUT")
        
        print(f"✓ Both modes should respect the {release_time:.3f}s release time setting")
        print()
    
    # Restore original settings
    settings['disable_release_fade'] = disable_release_fade
    with open(settings_file, 'w') as f:
        json.dump(settings, f, indent=4)
    
    print("=" * 60)
    print("✅ RELEASE TIME FIX APPLIED")
    print()
    print("Key Changes Made:")
    print("1. Both modes now use the same release_time duration")
    print("2. disable_release_fade = true:  Play for release_time, then HARD CUT")
    print("3. disable_release_fade = false: Play for release_time, then FADE OUT")
    print()
    print("Before Fix:")
    print("- disable_release_fade = true used 2.0 seconds (ignored user setting)")
    print("- disable_release_fade = false used user's release_time")
    print()
    print("After Fix:")
    print("- Both modes use user's release_time setting")
    print("- Only difference is fade vs hard cut")
    print()
    print(f"Your 0.050s release time will now work in both modes!")
    
    return True

def main():
    """Main test function."""
    return test_release_time_behavior()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
