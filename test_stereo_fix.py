#!/usr/bin/env python3
"""
Quick test to verify stereo channel fix is working correctly.
"""

import os
import sys
import numpy as np
from hexsf2newparser import SF2NewParser

def test_stereo_channels(sf2_path: str):
    """Test stereo channel handling specifically."""
    print(f"Testing stereo channels in: {sf2_path}")
    print("=" * 60)
    
    try:
        # Initialize and parse the SF2 file
        parser = SF2NewParser(sf2_path)
        parser.parse()
        parser.load_sample_data()
        
        print(f"✓ Loaded SF2 with {len(parser.sample_headers)} samples")
        
        # Find Grand Piano preset (usually preset 0)
        piano_preset_idx = 0
        for i, preset in enumerate(parser.presets):
            if 'grand' in preset.name.lower() and 'piano' in preset.name.lower():
                piano_preset_idx = i
                break
        
        print(f"Testing preset {piano_preset_idx}: '{parser.presets[piano_preset_idx].name}'")
        
        # Get sample mappings for middle C (note 60)
        sample_map = parser.get_samples_for_preset(piano_preset_idx)
        
        if 60 not in sample_map:
            print("No sample mapping found for middle C (note 60)")
            return False
        
        sample_id, generators = sample_map[60]
        header = parser.sample_headers[sample_id]
        
        print(f"\nMiddle C (note 60) uses sample {sample_id}: '{header.name}'")
        print(f"Sample type: {header.sample_type} ({parser._get_sample_type_string(header.sample_type)})")
        print(f"Sample link: {header.sample_link}")
        
        # Test both old and new methods
        print(f"\nTesting sample extraction methods:")
        
        # Test new stereo method
        stereo_sample = parser.get_stereo_sample(sample_id)
        if stereo_sample is not None:
            print(f"✓ get_stereo_sample(): {stereo_sample.shape}, duration: {len(stereo_sample)/header.sample_rate:.3f}s")
            
            if stereo_sample.shape[1] == 2:
                left_max = np.max(np.abs(stereo_sample[:, 0]))
                right_max = np.max(np.abs(stereo_sample[:, 1]))
                correlation = np.corrcoef(stereo_sample[:, 0], stereo_sample[:, 1])[0, 1]
                
                print(f"  Left channel max: {left_max:.6f}")
                print(f"  Right channel max: {right_max:.6f}")
                print(f"  Channel correlation: {correlation:.6f}")
                
                # Check for the "random instruments" problem
                if correlation < 0.5 and left_max > 0.01 and right_max > 0.01:
                    print(f"  ⚠ WARNING: Low correlation might indicate channel mixing issue!")
                elif abs(left_max - right_max) / max(left_max, right_max) > 0.8:
                    print(f"  ⚠ WARNING: Severe channel imbalance!")
                else:
                    print(f"  ✓ Stereo channels look good")
        else:
            print(f"✗ get_stereo_sample() failed")
            return False
        
        # Test old method for comparison
        old_sample = parser.get_sample(sample_id)
        if old_sample is not None:
            print(f"✓ get_sample(): {old_sample.shape}, duration: {len(old_sample)/header.sample_rate:.3f}s")
        else:
            print(f"✗ get_sample() failed")
        
        # Test a few more notes to see if the pattern is consistent
        test_notes = [48, 60, 72]  # C3, C4, C5
        print(f"\nTesting multiple notes for consistency:")
        
        for note in test_notes:
            if note in sample_map:
                test_sample_id, _ = sample_map[note]
                test_header = parser.sample_headers[test_sample_id]
                test_stereo = parser.get_stereo_sample(test_sample_id)
                
                if test_stereo is not None and test_stereo.shape[1] == 2:
                    left_max = np.max(np.abs(test_stereo[:, 0]))
                    right_max = np.max(np.abs(test_stereo[:, 1]))
                    correlation = np.corrcoef(test_stereo[:, 0], test_stereo[:, 1])[0, 1]
                    
                    status = "✓"
                    if correlation < 0.5:
                        status = "⚠"
                    
                    print(f"  Note {note}: {status} L={left_max:.4f}, R={right_max:.4f}, corr={correlation:.3f} (sample {test_sample_id})")
        
        print(f"\n" + "=" * 60)
        print("Test completed!")
        return True
        
    except Exception as e:
        print(f"✗ Error during test: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """Main test function."""
    # Try to find a soundfont
    test_paths = [
        "arachno.sf2",
        "Arachno.sf2", 
        "arachno-soundfont.sf2",
        "Arachno SoundFont - Version 1.0.sf2",
        "FluidR3_GM.sf2",
        "default.sf2"
    ]
    
    sf2_path = None
    for path in test_paths:
        if os.path.exists(path):
            sf2_path = path
            break
    
    if not sf2_path:
        print("No soundfont found. Please provide a path:")
        sf2_path = input("Enter SF2 file path: ").strip()
        if not sf2_path or not os.path.exists(sf2_path):
            print("Invalid or missing soundfont file.")
            return False
    
    return test_stereo_channels(sf2_path)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
