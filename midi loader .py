import struct
import math
import os
import random
import numpy as np
from pydub import AudioSegment
from tqdm import tqdm
import bisect
import mmap
import multiprocessing
from functools import lru_cache
import warnings
from typing import Dict, List, Tuple, Optional

warnings.filterwarnings('ignore')

# ====================
# MIDI Parsing with Memory Mapping
# ====================

def memory_map_file(file_path: str) -> mmap.mmap:
    with open(file_path, 'rb') as f:
        return mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ)

def read_uint32_be(mm: mmap.mmap, offset: int) -> int:
    return struct.unpack(">I", mm[offset:offset+4])[0]

def read_uint16_be(mm: mmap.mmap, offset: int) -> int:
    return struct.unpack(">H", mm[offset:offset+2])[0]

def read_var_len(mm: mmap.mmap, offset: int) -> <PERSON><PERSON>[int, int]:
    value = 0
    current_offset = offset
    while True:
        b = mm[current_offset]
        current_offset += 1
        value = (value << 7) | (b & 0x7F)
        if not (b & 0x80):
            break
    return value, current_offset - offset

def parse_header(mm: mmap.mmap, offset: int = 0) -> Tuple[Dict, int]:
    if mm[offset:offset+4] != b'MThd':
        raise ValueError("Invalid MIDI header")
    header_length = read_uint32_be(mm, offset+4)
    format_type = read_uint16_be(mm, offset+8)
    ntrks = read_uint16_be(mm, offset+10)
    division = read_uint16_be(mm, offset+12)
    header = {'format': format_type, 'ntrks': ntrks, 'division': division}
    print(f"MIDI Format: {format_type}, Tracks: {ntrks}, Division: {division}")
    return header, offset + 8 + header_length

def parse_event(mm: mmap.mmap, offset: int, running_status: Optional[int]) -> Tuple[Dict, Optional[int], int]:
    start_offset = offset
    delta_time, vlq_size = read_var_len(mm, offset)
    offset += vlq_size
    event_byte = mm[offset]
    offset += 1
    event = {"delta_time": delta_time}
    if event_byte == 0xFF:
        meta_type = mm[offset]
        offset += 1
        length, vlq_size = read_var_len(mm, offset)
        offset += vlq_size
        data = mm[offset:offset+length]
        offset += length
        event.update({"type": "meta", "meta_type": meta_type, "data": data})
        new_running_status = None
    elif event_byte in (0xF0, 0xF7):
        length, vlq_size = read_var_len(mm, offset)
        offset += vlq_size
        data = mm[offset:offset+length]
        offset += length
        event.update({"type": "sysex", "event_byte": event_byte, "data": data})
        new_running_status = None
    else:
        if event_byte & 0x80:
            status = event_byte
            new_running_status = status
        else:
            if running_status is None:
                raise ValueError("Running status used but no previous status available")
            status = running_status
            offset -= 1
            new_running_status = running_status
        event.update({"type": "midi", "status": status, "channel": status & 0x0F})
        event_type = status & 0xF0
        if event_type in (0xC0, 0xD0):
            data = mm[offset:offset+1]
            offset += 1
        else:
            data = mm[offset:offset+2]
            offset += 2
        event["data"] = list(data)
    event["size"] = offset - start_offset
    return event, new_running_status, offset

def parse_track(mm: mmap.mmap, offset: int, track_number: int) -> Tuple[List[Dict], int]:
    if mm[offset:offset+4] != b'MTrk':
        raise ValueError(f"Invalid track chunk at track {track_number}")
    track_length = read_uint32_be(mm, offset+4)
    offset += 8
    track_end = offset + track_length
    events = []
    running_status = None
    while offset < track_end:
        event, running_status, offset = parse_event(mm, offset, running_status)
        events.append(event)
    print(f"Track {track_number}: {len(events)} events parsed")
    return events, offset

# ====================
# Tempo Map Handling
# ====================

def find_tempo_events(tracks: List[List[Dict]]) -> List[Dict]:
    tempo_events = []
    for track_idx, track in enumerate(tracks):
        for event in track:
            if event['type'] == 'meta':
                if event['meta_type'] == 0x51 and len(event['data']) == 3:
                    tempo_micros = int.from_bytes(event['data'], byteorder='big')
                    if tempo_micros > 0:
                        tempo_events.append({
                            'tick': event['abs_tick'],
                            'tempo': tempo_micros,
                            'track': track_idx,
                            'type': 'tempo'
                        })
                elif event['meta_type'] == 0x58 and len(event['data']) == 4:
                    numerator = event['data'][0]
                    denominator = 2 ** event['data'][1]
                    clocks_per_click = event['data'][2]
                    thirty_seconds_per_quarter = event['data'][3]
                    tempo_events.append({
                        'tick': event['abs_tick'],
                        'numerator': numerator,
                        'denominator': denominator,
                        'clocks_per_click': clocks_per_click,
                        'thirty_seconds_per_quarter': thirty_seconds_per_quarter,
                        'track': track_idx,
                        'type': 'time_signature'
                    })
    tempo_events.sort(key=lambda x: (x['tick'], 0 if x['type'] == 'tempo' else 1))
    return tempo_events

class TempoMap:
    def __init__(self, division: int, fixed_bpm: Optional[float] = None):
        self.division = division
        self.segments: List[Dict] = []
        self.fixed_bpm = fixed_bpm
        self.time_signatures = []
        default_tempo = int(60000000 / (fixed_bpm if fixed_bpm else 120))
        self.add_tempo_segment(0, default_tempo)

    def add_tempo_segment(self, tick: int, tempo: int):
        if self.fixed_bpm:
            return
        try:
            bpm = 60000000 / tempo
            if 0.001 <= bpm <= 2000:
                self.segments.append({'tick': tick, 'tempo': tempo, 'bpm': bpm, 'cum_seconds': 0.0})
            else:
                print(f"Warning: Unusual BPM value: {bpm:.2f} at tick {tick}")
                clamped_bpm = np.clip(bpm, 0.001, 2000)
                clamped_tempo = int(60000000 / clamped_bpm)
                self.segments.append({'tick': tick, 'tempo': clamped_tempo, 'bpm': clamped_bpm, 'cum_seconds': 0.0})
        except Exception as e:
            print(f"Warning: Error processing tempo at tick {tick}: {e}")
            if self.segments:
                last_tempo = self.segments[-1]['tempo']
                self.segments.append({'tick': tick, 'tempo': last_tempo, 'bpm': 60000000 / last_tempo, 'cum_seconds': 0.0})

    def add_time_signature(self, tick: int, numerator: int, denominator: int):
        self.time_signatures.append({'tick': tick, 'numerator': numerator, 'denominator': denominator})

    def tick2second(self, tick: int, segment_tempo: int) -> float:
        try:
            division = self.division if self.division > 0 else 96
            if segment_tempo <= 0:
                segment_tempo = 500000
            return max(0.0, (tick * segment_tempo) / (division * 1000000))
        except Exception as e:
            print(f"Warning: Error in tick2second conversion: {e}")
            return 0.0

    def finalize(self):
        try:
            self.segments.sort(key=lambda x: x['tick'])
            for i in range(1, len(self.segments)):
                prev = self.segments[i-1]
                curr = self.segments[i]
                dt = curr['tick'] - prev['tick']
                if dt < 0:
                    dt = 0
                curr['cum_seconds'] = prev['cum_seconds'] + self.tick2second(dt, prev['tempo'])
        except Exception as e:
            print(f"Warning: Error during tempo map finalization: {e}")
            if self.segments:
                first_segment = self.segments[0]
                self.segments = [first_segment]

    def time_at_tick(self, tick: int) -> float:
        try:
            if not self.segments:
                raise ValueError("Tempo map has no segments")
            if tick < 0:
                tick = 0
            idx = bisect.bisect_right([s['tick'] for s in self.segments], tick) - 1
            idx = max(0, idx)
            segment = self.segments[idx]
            dt = tick - segment['tick']
            return segment['cum_seconds'] + self.tick2second(dt, segment['tempo'])
        except Exception as e:
            print(f"Warning: Error in time_at_tick: {e}")
            return 0.0

def build_tempo_map(tracks: List[List[Dict]], division: int, fixed_bpm: Optional[float] = None) -> TempoMap:
    tempo_map = TempoMap(division, fixed_bpm)
    if not fixed_bpm:
        tempo_events = find_tempo_events(tracks)
        if tempo_events:
            print(f"\nFound {len(tempo_events)} tempo/time signature events:")
            for event in tempo_events:
                if event['type'] == 'tempo':
                    bpm = 60000000 / event['tempo']
                    print(f"Track {event['track']}, Tick {event['tick']}: {bpm:.2f} BPM")
                    tempo_map.add_tempo_segment(event['tick'], event['tempo'])
                elif event['type'] == 'time_signature':
                    print(f"Track {event['track']}, Tick {event['tick']}: Time Signature {event['numerator']}/{event['denominator']}")
                    tempo_map.add_time_signature(event['tick'], event['numerator'], event['denominator'])
        else:
            print("\nNo tempo events found in any track, using default tempo of 120 BPM")
    tempo_map.finalize()
    return tempo_map

# ====================
# Note Event Processing and Pitch Bend Handling
# ====================

def add_absolute_ticks(tracks: List[List[Dict]]):
    for track in tracks:
        abs_tick = 0
        for event in track:
            abs_tick += event['delta_time']
            event['abs_tick'] = abs_tick

@lru_cache(maxsize=128)
def load_sample_cached(note_number: int) -> Tuple[np.ndarray, int, int, int]:
    filename = f"D:\\Downloads\\y30sample127\\{note_number}.wav"
    if not os.path.exists(filename):
        raise FileNotFoundError(f"Sample file {filename} not found")
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        sample = AudioSegment.from_wav(filename)
    sample_array = np.frombuffer(sample.raw_data, dtype=np.int16)
    sample_array = sample_array.reshape(-1, sample.channels)
    sample_array = sample_array.astype(np.int32)
    return sample_array, sample.frame_rate, sample.sample_width, sample.channels

class NoteEvent:
    def __init__(self, channel: int, note: int, start_time: float, end_time: float, velocity: int):
        self.channel = channel
        self.note = note
        self.start_time = start_time
        self.end_time = end_time
        self.velocity = velocity

    @property
    def duration(self) -> float:
        return self.end_time - self.start_time

def process_note_events(tracks: List[List[Dict]], tempo_map: TempoMap) -> Tuple[List[NoteEvent], Dict[int, List[Tuple[float, float]]]]:
    note_events = []
    active_notes: Dict[Tuple[int, int], List[Dict]] = {}
    pitch_bend_map: Dict[int, List[Tuple[float, float]]] = {}

    rpn_state: Dict[int, Dict[str, Optional[int]]] = {}
    pitch_bend_range: Dict[int, float] = {}
    for ch in range(16):
        rpn_state[ch] = {'RPN_MSB': None, 'RPN_LSB': None, 'fine': 0}
        pitch_bend_range[ch] = 2.0

    all_events = [(event, track_idx) for track_idx, track in enumerate(tracks) for event in track]
    all_events.sort(key=lambda x: x[0]['abs_tick'])

    current_bend: Dict[int, float] = {}

    for event, track_idx in all_events:
        event_time = tempo_map.time_at_tick(event['abs_tick'])
        if event['type'] != 'midi':
            continue
        status = event['status']
        event_type = status & 0xF0
        channel = event['channel']

        if event_type == 0xB0:
            cc_number, cc_value = event['data']
            if cc_number == 101:
                rpn_state[channel]['RPN_MSB'] = cc_value
            elif cc_number == 100:
                rpn_state[channel]['RPN_LSB'] = cc_value
            elif cc_number == 6:
                if rpn_state[channel]['RPN_MSB'] == 0 and rpn_state[channel]['RPN_LSB'] == 0:
                    pitch_bend_range[channel] = float(cc_value)
            elif cc_number == 38:
                if rpn_state[channel]['RPN_MSB'] == 0 and rpn_state[channel]['RPN_LSB'] == 0:
                    rpn_state[channel]['fine'] = cc_value
                    pitch_bend_range[channel] = float(pitch_bend_range[channel]) + (cc_value / 100.0)
            elif cc_number in (101, 100) and cc_value == 127:
                rpn_state[channel]['RPN_MSB'] = None
                rpn_state[channel]['RPN_LSB'] = None
            continue

        if event_type == 0xE0:
            if len(event['data']) < 2:
                continue
            lsb, msb = event['data']
            bend_value = (msb << 7) + lsb
            range_semitones = pitch_bend_range.get(channel, 2.0)
            semitone_offset = ((bend_value - 8192) / 8192) * range_semitones
            current_bend[channel] = semitone_offset
            pitch_bend_map.setdefault(channel, []).append((event_time, semitone_offset))
            continue

        if event_type == 0x90:
            note, velocity = event['data']
            if velocity == 0:
                key = (channel, note)
                if key in active_notes and active_notes[key]:
                    start_event = active_notes[key].pop(0)
                    note_events.append(NoteEvent(channel=channel, note=note, start_time=start_event['time'], end_time=event_time, velocity=start_event['velocity']))
            else:
                key = (channel, note)
                active_notes.setdefault(key, []).append({'time': event_time, 'velocity': velocity})
        elif event_type == 0x80:
            note, _ = event['data']
            key = (channel, note)
            if key in active_notes and active_notes[key]:
                start_event = active_notes[key].pop(0)
                note_events.append(NoteEvent(channel=channel, note=note, start_time=start_event['time'], end_time=event_time, velocity=start_event['velocity']))

    for (channel, note), starts in active_notes.items():
        for start in starts:
            note_events.append(NoteEvent(channel=channel, note=note, start_time=start['time'], end_time=start['time'], velocity=start['velocity']))

    return note_events, pitch_bend_map

# ---------------------------
# Polyphony Limiter and Per-Note Quantization
# ---------------------------

def apply_polyphony_limiter_raw(note_events: List[NoteEvent], polyphony_limit: int) -> List[NoteEvent]:
    """
    Apply the polyphony limiter using the original (unquantized) note times.
    """
    if polyphony_limit <= 0:
        return note_events

    events = []
    for note in note_events:
        events.append((note.start_time, 'start', note, note.velocity))
        events.append((note.end_time, 'end', note, note.velocity))
    
    def event_sort_key(event):
        time, typ, note, velocity = event
        # Ensure 'end' events come before 'start' events at the same time.
        type_order = 0 if typ == 'end' else 1
        if typ == 'start':
            return (time, type_order, -velocity)
        else:
            return (time, type_order, velocity)
    
    events.sort(key=event_sort_key)
    active_notes = []
    for time, typ, note, velocity in events:
        if typ == 'end':
            if note in active_notes:
                active_notes.remove(note)
        else:
            if len(active_notes) < polyphony_limit:
                active_notes.append(note)
            else:
                weakest = min(active_notes, key=lambda n: n.velocity)
                if note.velocity > weakest.velocity:
                    if weakest.end_time > time:
                        weakest.end_time = time
                    active_notes.remove(weakest)
                    active_notes.append(note)
                else:
                    note.end_time = note.start_time
    limited_notes = [n for n in note_events if n.end_time > n.start_time]
    limited_notes.sort(key=lambda n: n.start_time)
    return limited_notes

def quantize_note_individually(note: NoteEvent, processing_fps: int, fps_fluctuation: float) -> None:
    """
    Quantize a single note's start and end times using an individual random factor.
    """
    if processing_fps > 0:
        factor = random.uniform(1/fps_fluctuation, fps_fluctuation) if fps_fluctuation > 0 else 1.0
        effective_fps = processing_fps * factor
        dt = 1.0 / effective_fps
        note.start_time = round(note.start_time / dt) * dt
        note.end_time = round(note.end_time / dt) * dt

# ---------------------------
# Note Parameters and Audio Processing
# ---------------------------

class NoteParameters:
    def __init__(self):
        self.release_extension = 0.1

def dynamic_limiter(audio_data: np.ndarray, threshold_db: float = -0.1, attack_time: float = 0.005, release_time: float = 0.050, sample_rate: int = 44100) -> np.ndarray:
    audio_float = audio_data.astype(np.float32) / 32768.0
    threshold_linear = 10 ** (threshold_db / 20.0)
    amplitude = np.abs(audio_float)
    attack_coeff = np.exp(-1.0 / (sample_rate * attack_time))
    release_coeff = np.exp(-1.0 / (sample_rate * release_time))
    envelope = np.zeros_like(amplitude)
    envelope[0] = amplitude[0]
    for i in range(1, len(amplitude)):
        if amplitude[i] > envelope[i-1]:
            envelope[i] = attack_coeff * envelope[i-1] + (1 - attack_coeff) * amplitude[i]
        else:
            envelope[i] = release_coeff * envelope[i-1] + (1 - release_coeff) * amplitude[i]
    gain_reduction = np.ones_like(envelope)
    mask = envelope > threshold_linear
    gain_reduction[mask] = threshold_linear / envelope[mask]
    limited_audio = audio_float * gain_reduction
    limited_audio = np.clip(limited_audio, -0.99, 0.99)
    return (limited_audio * 32768.0).astype(np.int16)

def normalize_audio(audio_data: np.ndarray, target_peak: float = 0.95) -> np.ndarray:
    audio_float = audio_data.astype(np.float32) / 32768.0
    current_peak = np.max(np.abs(audio_float))
    if current_peak > 0:
        gain = target_peak / current_peak
        audio_float *= gain
    return np.clip(audio_float * 32768.0, -32768, 32767).astype(np.int16)

# ====================
# Audio Rendering with Pitch Bend Support
# ====================

class AudioRenderer:
    def __init__(self, sample_rate: int, channels: int = 1):
        self.sample_rate = sample_rate
        self.channels = channels
        self.buffer_size = 6**20
        self.workers = max(1, multiprocessing.cpu_count() - 1)
        self.note_params = NoteParameters()

    def resample_segment(self, segment: np.ndarray, output_length: int) -> np.ndarray:
        if len(segment) == 0:
            return np.zeros((output_length, segment.shape[1]), dtype=np.int32)
        x_old = np.linspace(0, 1, num=len(segment))
        x_new = np.linspace(0, 1, num=output_length)
        resampled = []
        for ch in range(segment.shape[1]):
            resampled_channel = np.interp(x_new, x_old, segment[:, ch])
            resampled.append(resampled_channel)
        return np.stack(resampled, axis=-1).astype(np.int32)

    def render(self, note_events: List[NoteEvent], final_duration_ms: int, pitch_bend_map: Dict[int, List[Tuple[float, float]]]) -> np.ndarray:
        final_samples = int((final_duration_ms / 1000) * self.sample_rate)
        final_mix = np.zeros((final_samples, 2), dtype=np.int32)
        note_events.sort(key=lambda x: x.start_time)
        for note in tqdm(note_events, desc="Rendering notes"):
            try:
                sample_array, sr, sw, ch = load_sample_cached(note.note)
                if sr != self.sample_rate:
                    continue
                mix_start = int(note.start_time * sr)
                effective_end = note.end_time + self.note_params.release_extension
                total_output_duration = effective_end - note.start_time
                total_output_samples = int(total_output_duration * sr)
                channel_bends = pitch_bend_map.get(note.channel, [])
                relevant_bends = [ (t, bend) for (t, bend) in channel_bends if note.start_time <= t <= note.end_time ]
                if not relevant_bends or relevant_bends[0][0] > note.start_time:
                    relevant_bends.insert(0, (note.start_time, 0.0))
                if relevant_bends[-1][0] < note.end_time:
                    relevant_bends.append((note.end_time, relevant_bends[-1][1]))
                relevant_bends.append((effective_end, relevant_bends[-1][1]))
                input_offset = 0
                note_output_segments = []
                for i in range(len(relevant_bends) - 1):
                    seg_start_time, bend = relevant_bends[i]
                    seg_end_time, _ = relevant_bends[i+1]
                    seg_duration = seg_end_time - seg_start_time
                    seg_output_samples = int(seg_duration * sr)
                    pitch_factor = 2 ** (bend / 12.0)
                    seg_input_samples = int(seg_output_samples * pitch_factor)
                    if input_offset + seg_input_samples <= len(sample_array):
                        input_seg = sample_array[input_offset: input_offset + seg_input_samples]
                    else:
                        input_seg = sample_array[input_offset:]
                        pad_width = seg_input_samples - len(input_seg)
                        input_seg = np.concatenate([input_seg, np.zeros((pad_width, sample_array.shape[1]), dtype=np.int32)], axis=0)
                    resampled_seg = self.resample_segment(input_seg, seg_output_samples)
                    note_output_segments.append(resampled_seg)
                    input_offset += seg_input_samples
                if note_output_segments:
                    note_audio = np.concatenate(note_output_segments, axis=0)
                else:
                    continue
                velocity_factor = (note.velocity / 127.0) ** 2
                note_audio = (note_audio * velocity_factor).astype(np.int32)
                end_index = mix_start + len(note_audio)
                if end_index > final_mix.shape[0]:
                    new_size = end_index
                    temp = np.zeros((new_size, 2), dtype=np.int32)
                    temp[:final_mix.shape[0]] = final_mix
                    final_mix = temp
                final_mix[mix_start:end_index] += note_audio
            except Exception as e:
                print(f"Error processing note {note.note} at time {note.start_time}: {e}")
                continue
        max_val = np.max(np.abs(final_mix))
        if max_val > 0:
            final_mix = (final_mix * 0.7 * 32768.0 / max_val).astype(np.int32)
        limited_mix = np.zeros_like(final_mix, dtype=np.int16)
        for channel in range(2):
            limited_mix[:, channel] = dynamic_limiter(
                final_mix[:, channel],
                threshold_db=-0.1,
                attack_time=0.005,
                release_time=0.050,
                sample_rate=self.sample_rate
            )
        return limited_mix

def export_audio(audio_data: np.ndarray, sample_rate: int, sample_width: int, channels: int, output_filename: str):
    audio_data = np.clip(audio_data, -32768, 32767).astype(np.int16)
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        audio = AudioSegment(
            audio_data.tobytes(),
            sample_width=sample_width,
            frame_rate=sample_rate,
            channels=2
        )
        audio.export(output_filename, format="mp3", bitrate="320k")

# ====================
# Main
# ====================

def main():
    midi_file_path = r"D:\Downloads\Hypernova FINAL\Hypernova.mid.AR.mid"
    output_audio_file = r'ren5434532245543n.mp3'
    try:
        with memory_map_file(midi_file_path) as mm:
            header, offset = parse_header(mm)
            tracks = []
            for i in range(header['ntrks']):
                track_events, offset = parse_track(mm, offset, i)
                tracks.append(track_events)
        add_absolute_ticks(tracks)
        tempo_map = build_tempo_map(tracks, header['division'])
        note_events, pitch_bend_map = process_note_events(tracks, tempo_map)
        if not note_events:
            raise ValueError("No notes found in MIDI file")
        
        # Settings:
        POLYPHONY_LIMIT = 0      # maximum 8 active voices (0 for unlimited)
        PROCESSING_FPS = 0     # base quantization rate
        FPS_FLUCTUATION = 0     # each note gets its own random fluctuation factor

        # First, apply polyphony limiting on the unquantized note events.
        limited_notes = apply_polyphony_limiter_raw(note_events, POLYPHONY_LIMIT)
        
        # Then, quantize each note individually with its own random factor.
        for note in limited_notes:
            quantize_note_individually(note, PROCESSING_FPS, FPS_FLUCTUATION)
        
        # Instead of taking the max end time from limited notes, compute final duration from all MIDI events.
        all_ticks = [event['abs_tick'] for track in tracks for event in track]
        last_tick = max(all_ticks)
        final_duration = tempo_map.time_at_tick(last_tick)
        final_duration_ms = int((final_duration + 1.0) * 1000)
        
        # Use the sample from the first note (assuming all samples have the same sample rate)
        first_note = limited_notes[0].note
        _, sample_rate, sample_width, channels = load_sample_cached(first_note)
        renderer = AudioRenderer(sample_rate, channels)
        final_mix = renderer.render(limited_notes, final_duration_ms, pitch_bend_map)
        export_audio(final_mix, sample_rate, sample_width, channels, output_audio_file)
        print(f"\nAudio rendered and saved to: {output_audio_file}")
    except Exception as e:
        print(f"Error during processing: {e}")
        raise

if __name__ == '__main__':
    main()