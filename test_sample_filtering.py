#!/usr/bin/env python3
"""
Test script to verify that the SF2 parser is correctly filtering out problematic short samples.
This will test the updated get_sample() and get_raw_sample_data() methods.
"""

import json
import sys
from hexsf2newparser import SF2<PERSON><PERSON><PERSON><PERSON><PERSON>

def test_sample_filtering():
    """Test that the SF2 parser extends short samples using loop points instead of filtering them out."""
    
    # Load settings to get the SF2 file path
    try:
        with open('hexsyn_settings.json', 'r') as f:
            settings = json.load(f)
        sf2_path = settings.get('sf2_path', '')
        if not sf2_path:
            print("Error: No SF2 file path found in settings")
            return False
    except Exception as e:
        print(f"Error loading settings: {e}")
        return False
    
    print(f"Testing sample filtering with SF2 file: {sf2_path}")
    
    # Initialize parser
    parser = SF2NewParser(sf2_path)
    
    try:
        # Parse the SF2 file
        parser.parse()
        print(f"Successfully parsed SF2 file with {len(parser.sample_headers)} samples")
        
        # Test loop extension for short samples
        filtered_count = 0
        loaded_count = 0
        extended_count = 0
        quiet_filtered = 0

        print("\nTesting sample loop extension...")

        for sample_id in range(len(parser.sample_headers)):
            header = parser.sample_headers[sample_id]
            original_sample_length = header.end - header.start
            original_duration_ms = (original_sample_length / header.sample_rate) * 1000 if header.sample_rate > 0 else 0

            # Test get_sample method
            sample_data = parser.get_sample(sample_id)

            if sample_data is None:
                filtered_count += 1
                quiet_filtered += 1
                if sample_id < 5:  # Show first few filtered samples
                    print(f"  Filtered quiet sample {sample_id} ('{header.name}')")
            else:
                loaded_count += 1
                actual_duration_ms = (len(sample_data) / header.sample_rate) * 1000 if header.sample_rate > 0 else 0

                # Check if sample was extended
                if len(sample_data) > original_sample_length:
                    extended_count += 1
                    if extended_count <= 10:  # Show first 10 extended samples
                        print(f"  Extended sample {sample_id} ('{header.name}'): {original_duration_ms:.1f}ms -> {actual_duration_ms:.1f}ms")
                elif sample_id < 5 and original_duration_ms < 100:  # Show first few short samples that weren't extended
                    loop_info = parser.get_sample_loop_info(sample_id)
                    has_loop = loop_info['has_loop'] if loop_info else False
                    print(f"  Short sample {sample_id} ('{header.name}'): {original_duration_ms:.1f}ms, has_loop={has_loop}")

        print(f"\nLoop Extension Results:")
        print(f"  Total samples: {len(parser.sample_headers)}")
        print(f"  Successfully loaded: {loaded_count}")
        print(f"  Extended using loops: {extended_count}")
        print(f"  Filtered out (quiet): {quiet_filtered}")
        
        # Test stereo sample filtering
        print(f"\nTesting stereo sample filtering...")
        stereo_filtered = 0
        stereo_loaded = 0
        
        for sample_id in range(min(50, len(parser.sample_headers))):  # Test first 50 samples
            stereo_data = parser.get_stereo_sample(sample_id)
            if stereo_data is None:
                stereo_filtered += 1
            else:
                stereo_loaded += 1
        
        print(f"  Stereo samples tested: 50")
        print(f"  Successfully loaded: {stereo_loaded}")
        print(f"  Filtered out: {stereo_filtered}")
        
        # Success if we extended some short samples using loops
        if extended_count > 0:
            print(f"\n✓ SUCCESS: Extended {extended_count} short samples using loop points to prevent tapping sounds")
            return True
        else:
            print(f"\n⚠ WARNING: No short samples were extended - this may indicate the loop extension isn't working")
            return False
            
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Parser doesn't have a close method
        pass

if __name__ == "__main__":
    print("SF2 Sample Loop Extension Test")
    print("=" * 50)

    success = test_sample_filtering()

    if success:
        print("\n✓ Test completed successfully - sample loop extension is working!")
        sys.exit(0)
    else:
        print("\n✗ Test failed - sample loop extension may not be working correctly")
        sys.exit(1)
